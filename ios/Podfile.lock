PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (10.27.0):
    - Firebase/Core
  - Firebase/Core (10.27.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.27.0)
  - Firebase/CoreOnly (10.27.0):
    - FirebaseCore (= 10.27.0)
  - Firebase/Crashlytics (10.27.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.27.0)
  - Firebase/Messaging (10.27.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.27.0)
  - Firebase/RemoteConfig (10.27.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.27.0)
  - firebase_analytics (11.0.1):
    - Firebase/Analytics (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_core (3.1.1):
    - Firebase/CoreOnly (= 10.27.0)
    - Flutter
  - firebase_crashlytics (4.0.2):
    - Firebase/Crashlytics (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.0.2):
    - Firebase/Messaging (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.0.2):
    - Firebase/RemoteConfig (= 10.27.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.27.0):
    - FirebaseAnalytics/AdIdSupport (= 10.27.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.27.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.27.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.27.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_contact_picker (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleAppMeasurement (10.27.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.27.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.27.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - Protobuf (3.29.2)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - ReachabilitySwift (5.2.4)
  - reactive_ble_mobile (0.0.1):
    - Flutter
    - FlutterMacOS
    - Protobuf (~> 3.5)
    - SwiftProtobuf (~> 1.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SwiftProtobuf (1.28.2)
  - telephony (0.0.1):
    - Flutter
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_contact_picker (from `.symlinks/plugins/flutter_native_contact_picker/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - reactive_ble_mobile (from `.symlinks/plugins/reactive_ble_mobile/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - telephony (from `.symlinks/plugins/telephony/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - Protobuf
    - ReachabilitySwift
    - SwiftProtobuf
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_contact_picker:
    :path: ".symlinks/plugins/flutter_native_contact_picker/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  reactive_ble_mobile:
    :path: ".symlinks/plugins/reactive_ble_mobile/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  telephony:
    :path: ".symlinks/plugins/telephony/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  Firebase: 26b040b20866a55f55eb3611b9fcf3ae64816b86
  firebase_analytics: 0627e95b73eb9e04f59167687ed5bc3f6fb50f23
  firebase_core: f8d0424c45e0f1e596811085fc12c638d628457c
  firebase_crashlytics: 39ca2155bac4fa2eec0aec9f0eb5e938a08bca23
  firebase_messaging: 8b29edaf5adfd3b52b5bfa5af8128c44164670c6
  firebase_remote_config: 962876d64b52d7710d756ea85e27c768002dc628
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: f9211b719db260cc91aebee8bb539cb367d0dfd1
  FirebaseCore: a2b95ae4ce7c83ceecfbbbe3b6f1cddc7415a808
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 81ea6ec96519388687f6061beb838a8eec482293
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 585984d0a1df120617eb10b44cad8968b859815e
  FirebaseRemoteConfig: 37a2ba3c8c454be8553a41ba1a2f4a4f0b845670
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  flutter_native_contact_picker: 390f08cc44058386ee91499f05360d647dddfc66
  fluttertoast: 9f2f8e81bb5ce18facb9748d7855bf5a756fe3db
  google_maps_flutter_ios: 5bc2be60ad012e79b182ce0fb0ef5030a50fb03e
  GoogleAppMeasurement: f65fc137531af9ad647f1c0a42f3b6a4d3a98049
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  Protobuf: ba5d83b2201386fec27d484c099cac510ea5c169
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  reactive_ble_mobile: 856183aeda09f2a676bbaecbea8915c3cb1d33df
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  SwiftProtobuf: 4dbaffec76a39a8dc5da23b40af1a5dc01a4c02d
  telephony: c41768fae9fb5495781b05a72004106ca33ec777
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  vibration: 7d883d141656a1c1a6d8d238616b2042a51a1241
  wakelock_plus: 78ec7c5b202cab7761af8e2b2b3d0671be6c4ae1
  webview_flutter_wkwebview: 2a23822e9039b7b1bc52e5add778e5d89ad488d1

PODFILE CHECKSUM: e60e17f8bfffff789408fce3f968c37c5c63400e

COCOAPODS: 1.15.2
