import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../branding/branding.dart';
import '../../common/constant.dart';
import '../../common/dimensions.dart';
import '../login.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  _OnboardingScreenState createState() {
    return _OnboardingScreenState();
  }
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int currentIndex = 0;

  List<Map<String, String>> onboardingData = [
    {
      'title': 'Monitor Battery and Range',
      'description':
          'Track battery life and remaining mileage to ensure a worry-free journey.',
      'image': onboardingScreenImages['battery']!,
    },
    {
      'title': 'Analyze Your Performance',
      'description':
          'View detailed insights on your driving habits, trip history, and fuel savings.',
      'image': onboardingScreenImages['performance']!,
    }
  ];

  void onNext() async {
    if (currentIndex == onboardingData.length - 1) {
      await PreferenceHelper.setOnboardingDone();
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (_) => LoginScreen(
                    settings: [
                      privacyPolicySetting!,
                      termsAndConditionsSetting!
                    ],
                  )));
    } else {
      setState(() {
        currentIndex++;
      });
    }
  }

  void onSkip() async {
    await PreferenceHelper.setOnboardingDone();
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (_) => LoginScreen(
                  settings: [privacyPolicySetting!, termsAndConditionsSetting!],
                )));
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Text(
                    onboardingData[currentIndex]['title']!,
                    style: poppinsTextStyle(24 / 414 * dimensions.width,
                        loginThemeColor, FontWeight.w900),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    onboardingData[currentIndex]['description']!,
                    style: poppinsTextStyle(20 / 414 * dimensions.width,
                        colorBlack, FontWeight.w400),
                  ),
                ),
              ],
            ),
            Image.asset(
              onboardingData[currentIndex]['image']!,
              width: double.infinity,
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Skip Button without Background and Grey Text
                  TextButton(
                    onPressed: onSkip,
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey, // Grey Text Color
                    ),
                    child: const Text("Skip"),
                  ),

                  // Next Button with White Background and Black Text
                  ElevatedButton(
                    onPressed: onNext,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white, // White Background
                      foregroundColor: Colors.black87, // Text Color
                      elevation: 8, // Shadow Elevation
                      shadowColor:
                          Colors.black.withOpacity(0.2), // Light Shadow
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(30), // Circular Border Radius
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 30, vertical: 12), // Button Size
                    ),
                    child: Text(
                      currentIndex == onboardingData.length - 1
                          ? "Finish"
                          : "Next",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF3E4A5B), // Text Color like in Image
                      ),
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}

class PreferenceHelper {
  static Future<void> setOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('onboarding_done', true);
  }

  static Future<bool> isOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('onboarding_done') ?? false;
  }
}
