import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/screens/vehicle/vehicle_details_page.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_detail_page.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_page.dart';
import 'package:nds_app/screens/vehicle/vehicles_list.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

class VehicleConnectionPage extends StatefulWidget {
  const VehicleConnectionPage({super.key});

  @override
  State<VehicleConnectionPage> createState() => _VehicleConnectionPageState();
}

class _VehicleConnectionPageState extends State<VehicleConnectionPage> {
  late Widget activeScreen;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicle Screen',
      'screen_class': widget.runtimeType.toString(),
    });

    activeScreen = VehiclesList(switchScreen);
    super.initState();
  }

  void switchScreen(Vehicle v) {
    setState(() {
      activeScreen = VehicleDetailsPage(v, onBackPressed, () => vehicleHealthPressed(v));
    });
  }

  void onBackPressed() {
    setState(() {
      activeScreen = VehiclesList(switchScreen);
    });
  }

  void vehicleHealthPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleHealthPage(
        imei: v.regNo,
        onBackPressed: () => vehicleHealthOnBackPressed(v),
        onHealthDetailPressed: (String partType, String partLabel) => 
            vehicleHealthDetailPressed(v, partType, partLabel),
        vehicle: v,
      );
    });
  }

  void vehicleHealthOnBackPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleDetailsPage(v, onBackPressed, () => vehicleHealthPressed(v));
    });
  }

  void vehicleHealthDetailPressed(Vehicle v, String partType, String partLabel) {
    setState(() {
      activeScreen = VehicleHealthDetailPage(
        onBackPressed: () => vehicleHealthDetailOnBackPressed(v),
        imei: v.regNo,
        partType: partType,
        partLabel: partLabel,
        vehicle: v,
      );
    });
  }

  void vehicleHealthDetailOnBackPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleHealthPage(
        imei: v.regNo,
        onBackPressed: () => vehicleHealthOnBackPressed(v),
        onHealthDetailPressed: (String partType, String partLabel) => 
            vehicleHealthDetailPressed(v, partType, partLabel),
        vehicle: v,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return activeScreen;
  }
}
