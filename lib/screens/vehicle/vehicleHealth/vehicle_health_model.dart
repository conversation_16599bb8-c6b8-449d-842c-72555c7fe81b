class VehiclePart {
  final int partId;
  final String partType;
  final List<String> partLabel;
  final String info;
  final String status;

  VehiclePart({
    required this.partId,
    required this.partType,
    required this.partLabel,
    required this.info,
    required this.status,
  });

  factory VehiclePart.fromJson(Map<String, dynamic> json) {
    return VehiclePart(
      partId: json['partId'],
      partType: json['partType'],
      partLabel: List<String>.from(json['partLabel']),
      info: json['info'],
      status: json['status'],
    );
  }
}

class ProfileImageCoordinate {
  final String partType;
  final String partLabel;
  final double x;
  final double y;

  ProfileImageCoordinate({required this.partType, required this.partLabel, required this.x, required this.y});

  factory ProfileImageCoordinate.fromJson(Map<String, dynamic> json) {
    return ProfileImageCoordinate(
      partType: json['partType'],
      partLabel: json['partLabel'],
      x: json['x'],
      y: json['y'],
    );
  }
}

class ProfileImageData {
  final String url;
  final List<ProfileImageCoordinate> coordinates;

  ProfileImageData({required this.url, required this.coordinates});

  factory ProfileImageData.fromJson(Map<String, dynamic> json) {
    return ProfileImageData(
      url: json['url'],
      coordinates: (json['partListCoordinates'] as List)
          .map((coord) => ProfileImageCoordinate.fromJson(coord))
          .toList(),
    );
  }
}