import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/battery_alert.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/battery_info.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/vehicle_banner.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/constant.dart';
import '../../common/dimensions.dart';
import '../../common/image_urls.dart';
import '../../common/shared_preferences_keys.dart';
import '../../constant/action.dart';
import '../../constant/api_urls.dart';
import '../../models/user_info.dart';
import '../../models/vehicle_info.dart';
import '../../services/api_service.dart';

class HomeScreen extends StatefulWidget {
  final Function dashboardAction;
  const HomeScreen({
    super.key,
    required this.dashboardAction,
  });
  @override
  State<HomeScreen> createState() => HomeScreenState();
}

class HomeScreenState extends State<HomeScreen> {
  VehicleInfo vehicleInfo = VehicleInfo();
  late String homeUserDisplayName;
  late String imageUrl;
  late ScrollController _scrollController;
  VehicleStatusDataStream vehicleStatusDataStream = VehicleStatusDataStream();
  VehicleDataStream vehicleDataStream = VehicleDataStream();
  Timer? _scrollLogTimer;
  LogScreenTrackingEvent logScreenTrackingEvent = LogScreenTrackingEvent();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_screen': 'Home Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    imageUrl = "";
    homeUserDisplayName = homeDisplayNameOptions[0];

    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  _scrollListener() {
    if (_scrollLogTimer?.isActive ?? false) return;
    _scrollLogTimer = Timer(const Duration(seconds: 1), () {
      logScreenTrackingEvent.logScreenView(
          eventName: trackingLabels['ScrollHomeScreenAction']!);
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState == ConnectionState.done) {
          widget = SizedBox(
            height: 775 / 896 * dimensions.height,
            child: Scrollbar(
              controller: _scrollController,
              radius: const Radius.circular(20),
              thickness: 5,
              child: SingleChildScrollView(
                controller: _scrollController,
                scrollDirection: Axis.vertical,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                        visible: isB2CUser,
                        child: VehicleBanner(
                            imageUrl: imageUrl,
                            homeUserDiplayName: homeDisplayNameOptions[0]
                                    .contains(homeUserDisplayName)
                                ? userName
                                : vehicleInfo.regNo ?? "")),
                    Padding(
                      padding: EdgeInsets.only(
                          left: 14 / 414 * dimensions.width,
                          right: 14 / 414 * dimensions.width),
                      child: Column(
                        children: [
                          Visibility(
                              visible: isB2CUser,
                              child: BatteryInfo(vehicleInfo: vehicleInfo)),
                          Visibility(
                            visible: isB2CUser,
                            child: SizedBox(
                              height: 16 / 896 * dimensions.height,
                            ),
                          ),
                          Visibility(
                            visible: !isB2CUser,
                            child: UserAndVehicleName(
                              currentVehicleStatus: currentVehicleStatus,
                              vehicleInfo: vehicleInfo,
                              firstName: userInfo?.firstName ?? "",
                            ),
                          ),
                          Visibility(
                            visible: currentVehicleStatus ==
                                    VehicleStatus.disconnected ||
                                isB2CUser,
                            child: ScanAndFuelSaving(
                                action: () {}, userInfo: userInfo),
                          ),
                          Visibility(
                            visible: currentVehicleStatus ==
                                    VehicleStatus.connected &&
                                !isB2CUser,
                            child: BatteryAndRidingModes(
                              vehicleInfo: vehicleInfo,
                              userInfo: userInfo,
                            ),
                          ),
                          SizedBox(
                            height: 16 / 896 * dimensions.height,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 6 / 414 * dimensions.width),
                            child: const MapAndAvailableVehicle(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return widget;
      },
      future: loadData(),
    );
  }

  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    JsonDecoder decoder = const JsonDecoder();

    if (userInfo == null) {
      userInfo = await getUserInfo(decoder);
      userName = userInfo?.firstName ?? "";

      if (userInfo?.connectedVehicleImei != null) {
        pref.setString(
            connectedVehicleImeiNo, userInfo?.connectedVehicleImei! ?? "");
        currentVehicleStatus = VehicleStatus.connected;
        int statusCode = await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);

        if (statusCode != 200) {
          isVehicleInfoAlertMessageExist = true;
        }

        vehicleStatusDataStream
            .updateVehicleStatusResponse(VehicleStatus.connected);
        if (vehicleInfoConstant != null) {
          vehicleInfo = vehicleInfoConstant ?? VehicleInfo();
        } else {
          vehicleInfo = VehicleInfo();
        }
        vehicleDataStream.updateVehicleInfo(vehicleInfo);
      }
    } else {
      if (userInfo?.connectedVehicleImei != "" &&
          userInfo?.connectedVehicleImei != null) {
        await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);
      }
      if (isB2CUser) {
        UserInfo? freshUserInfo = await getUserInfo(decoder);
        if (freshUserInfo != null) {
          userInfo = freshUserInfo;
          // Ensure UI updates by triggering streams
          if (vehicleInfoConstant != null) {
            vehicleInfo = vehicleInfoConstant!;
            vehicleDataStream.updateVehicleInfo(vehicleInfo);
          }
        }
      } else {
        vehicleInfo = vehicleInfoConstant!;
      }

      if ((vehicleInfo.charge ?? 0) < 5 &&
          isB2CUser &&
          (vehicleInfo.batteryConnected ?? false)) {
        showBatteryLowAlert();
      }
      if ((vehicleInfo.batteryTemperature ?? 0) > 40 && isB2CUser) {
        showBatteryTemperatureAlert();
      }
    }
    homeUserDisplayName =
        pref.getString(homeDisplayNameKey) ?? homeDisplayNameOptions[0];

    vehicleInfo.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url!;
        }
      },
    );
  }

  Future<UserInfo?> getUserInfo(JsonDecoder decoder) async {
    http.Response userInfoResponse =
        await BackendApi.initiateGetCall(ApiUrls.userInfo);
    Map<String, dynamic> userResponse = decoder.convert(userInfoResponse.body);
    return UserInfo.fromJson(userResponse);
  }

  void showBatteryLowAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BatteryStatusAlert(
          message: homeScreenText["battery_low"]!,
        );
      },
    );
  }

  void showBatteryTemperatureAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BatteryStatusAlert(
          message: homeScreenText["battery_temperature"]!,
        );
      },
    );
  }

  @override
  void dispose() {
    _scrollLogTimer?.cancel();
    super.dispose();
  }
}
