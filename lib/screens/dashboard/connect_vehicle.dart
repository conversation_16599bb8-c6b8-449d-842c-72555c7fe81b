import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:nds_app/blocs/connect/connect_events.dart';
import 'package:nds_app/blocs/connect/connect_stream.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/screens/dashboard/dashboard.dart';
import 'package:nds_app/utils/container_grid.dart';
import 'package:nds_app/utils/extension.dart';
//import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/connect/connect_view_toggle_button.dart';
import 'package:vibration/vibration.dart';
import 'package:synchronized/synchronized.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:wakelock_plus/wakelock_plus.dart';


class ConnectVehicle extends StatefulWidget {
  const ConnectVehicle({super.key});

  @override
  State<ConnectVehicle> createState() => _ConnectVehicleState();
}

class _ConnectVehicleState extends State<ConnectVehicle> {
  final _streams = ConnectScreenStream();
  late List<TextEditingController> _codeContainerControllers;
  late List<FocusNode> _codeContainerFocusNodes;

  bool isConnectChassisCodeViewEnabled = false;
  String? message;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  MobileScannerController? controller;
  late Color color;
  late ColorType colorType;
  bool isFlashOn = false;

  @override
  void initState() {
    WakelockPlus.enable();
    LogScreenTrackingEvent().logScreenView(
        eventName: 'screen_view',
        parameters: {
          'screen_name': 'Connect Vehicle Screen',
          'screen_class': runtimeType.toString()
        });
    controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      formats: [BarcodeFormat.qrCode],
      facing: CameraFacing.back,
      torchEnabled: false,
    );
    super.initState();
    
    // Add a short delay to ensure UI is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        // Ensure the UI is fully visible
      });
    });

    _codeContainerControllers = [];
    _codeContainerFocusNodes = [];

    for (int i = 0; i < _streams.streams.length; i++) {
      _codeContainerControllers.add(TextEditingController());
      _codeContainerFocusNodes.add(FocusNode());
    }
    // _codeContainerFocusNodes[0].requestFocus();
    _streams.code.listen((event) async {
      if (event.length == _streams.streams.length) {
        FocusManager.instance.primaryFocus?.unfocus();
        // ignore: use_build_context_synchronously
        if (mounted) {
          message = await DialogAction.connect.action(
            context: context,
            code: event,
          );
        }

        _streams.eventSink.add(MessageEvent(message: message ?? ""));
      } else {
        _streams.eventSink.add(MessageEvent(message: ""));
      }
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    final height = dimensions.height;
    final width = dimensions.width;
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();

    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: 20 / 414 * width, vertical: 54 / 896 * height),
          child: Column(
            children: [
              Stack(
                children: [
                  BlocBuilder<ConnectVehicleToogleBloc,
                      ConnectVehicleToogleState>(
                    builder: (context, state) {
                      return state.isSwitchRight
                          ? _buildChassisCodeView(height, width, themeMode)
                          : Stack(
                              children: [
                                Align(
                                  alignment: Alignment.center,
                                  child: Container(
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        top: 260 / 896 * dimensions.height),
                                    decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .scaffoldBackgroundColor,
                                        borderRadius: BorderRadius.circular(
                                            8 / 414 * width)),
                                    height: 284 / 896 * height,
                                    width: 284 / 414 * width,
                                    child: _buildQrView(context),
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.center,
                                  child: Container(
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        top: 260 / 896 * dimensions.height),
                                    child: CustomPaint(
                                      painter: GridPainter(
                                          gridLineColor: colorGrey500,
                                          gridCount: 3),
                                      child: Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            borderRadius: BorderRadius.circular(
                                                8 / 414 * width)),
                                        height: 284 / 896 * height,
                                        width: 284 / 414 * width,
                                      ),
                                    ),
                                  ),
                                ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 140 / 896 * dimensions.height,
                                      width: width,
                                    ),
                                    SizedBox(
                                      height: 36 / 896 * dimensions.height,
                                      width: 36 / 414 * dimensions.width,
                                      child: const FittedBox(
                                        fit: BoxFit.fill,
                                        child: Icon(Icons.qr_code_scanner_sharp,
                                            color: colorGrey800),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 8 / 896 * dimensions.height,
                                    ),
                                    Text(
                                      connectVehicleText['text6']!,
                                      style: poppinsTextStyle(
                                          12 / 414 * dimensions.width,
                                          colorGrey800,
                                          FontWeight.w500),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                                Align(
                                  alignment: Alignment.center,
                                  child: GestureDetector(
                                    onTap: () async {
                                      await controller?.toggleTorch();
                                      setState(() {
                                        isFlashOn = !isFlashOn;
                                      });
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          top: 610 / 896 * dimensions.height),
                                      alignment: Alignment.center,
                                      width: 100 / 414 * dimensions.width,
                                      height: 100 / 414 * dimensions.width,
                                      decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: themeMode == ThemeMode.light
                                              ? colorWhite
                                              : colorBlack,
                                          border: Border.all(
                                              color: colorGrey300, width: 1),
                                          gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: themeMode ==
                                                      ThemeMode.light
                                                  ? [colorWhite, colorGrey200]
                                                  : [colorBlack, colorGrey700]),
                                          boxShadow: themeMode ==
                                                  ThemeMode.light
                                              ? [
                                                  BoxShadow(
                                                    color: colorGrey300
                                                        .withOpacity(0.7),
                                                    offset: const Offset(6, 6),
                                                    spreadRadius: 3,
                                                    blurRadius: 5,
                                                  ),
                                                  BoxShadow(
                                                    color: colorWhite
                                                        .withOpacity(0.7),
                                                    offset:
                                                        const Offset(-6, -6),
                                                    spreadRadius: 3,
                                                    blurRadius: 5,
                                                  ),
                                                ]
                                              : [
                                                  BoxShadow(
                                                    color: colorGrey700
                                                        .withOpacity(0.7),
                                                    offset: const Offset(6, 6),
                                                    spreadRadius: 3,
                                                    blurRadius: 5,
                                                  ),
                                                  BoxShadow(
                                                    color: colorBlack
                                                        .withOpacity(0.7),
                                                    offset:
                                                        const Offset(-6, -6),
                                                    spreadRadius: 3,
                                                    blurRadius: 5,
                                                  ),
                                                ]),
                                      child: Center(
                                        child: SizedBox(
                                            width: 48 / 414 * dimensions.width,
                                            height: 48 / 414 * dimensions.width,
                                            child: FittedBox(
                                              child: Icon(isFlashOn
                                                  ? Icons.flashlight_on
                                                  : Icons.flashlight_off),
                                            )),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                    },
                  ),
                  Column(
                    children: [
                      Align(
                        alignment: Alignment.topRight,
                        child: InkWell(
                          onTap: () async {
                            FocusManager.instance.primaryFocus?.unfocus();
                            closePageOperation(200);
                          },
                          child: Padding(
                            padding: EdgeInsets.all(8 / 414 * width),
                            child: SizedBox(
                                height: 24 / 896 * height,
                                width: 24 / 414 * width,
                                child: const FittedBox(
                                  fit: BoxFit.fill,
                                  child: Icon(
                                    Icons.close,
                                    color: colorWhite,
                                  ),
                                )),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 4 / 896 * height,
                      ),
                      ConnectViewToggleButton(
                        color: color,
                        colorType: colorType,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  Widget _buildChassisCodeView(
      double height, double width, ThemeMode themeMode) {
    return Column(
      children: [
        SizedBox(
          height: 225 / 896 * height,
        ),
        SizedBox(
          width: 44 / 414 * width,
          height: 44 / 896 * height,
          child: Image.asset(
            connectVehicleImages["password"]!,
            color: Theme.of(context).textTheme.headlineLarge?.color,
            fit: BoxFit.fill,
          ),
        ),
        SizedBox(
          height: 8 / 896 * height,
        ),

        Text(
          connectVehicleText["text5"]!,
          style: Theme.of(context).textTheme.labelMedium,
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 8 / 896 * height,
        ),
        Text(
          connectVehicleText["text2"]!,
          style: Theme.of(context).textTheme.labelMedium,
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 32 / 896 * height,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [getTextFieldContainers(width, height, themeMode)],
        ),
        SizedBox(
          height: 16 / 896 * height,
        ),
        StreamBuilder<String>(
            stream: _streams.message,
            initialData: "",
            builder: (context, snapshot) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 50 / 414 * width,
                  ),
                  Text(snapshot.data != "" ? (message ?? "") : "",
                      style: poppinsTextStyle(
                          12 / 414 * width, colorRed, FontWeight.w200)),
                ],
              );
            }),
        SizedBox(
          height: 16 / 896 * height,
        ),
        // Padding(
        //   padding: EdgeInsets.only(left: 95 / 414 * width),
        //   child: const UserConnectionTypeToggleButton(
        //     textColor: colorGrey300,
        //   ),
        // )
      ],
    );
  }

  Widget _buildQrView(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    var scanArea = dimensions.width;

    // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.
    // To ensure the Scanner view is properly sizes after rotation
    // we need to listen for Flutter SizeChanged notification and update controller
    return Stack(
      children: [
        MobileScanner(
          key: qrKey,
          controller: controller,
          onDetect: (BarcodeCapture capture) {
            final List<Barcode> barcodes = capture.barcodes;
            if (barcodes.isNotEmpty) {
              Vibration.vibrate(duration: 500, amplitude: 200);
              tryLogin(barcodes.first);
            }
          },
          onDetectError: _onPermissionSet,
        ),
        Center(
          child: Container(
            width: 284 / 414 * scanArea,
            height: 284 / 414 * scanArea,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onPermissionSet(Object error, StackTrace stackTrace) {
    log('${DateTime.now().toIso8601String()}_onPermissionSet $error');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          connectVehicleText['error1']!,
        ),
      ),
    );
  }

  final _lock = Lock();
  bool _isExecuting = false;
  Future<String?> tryLogin(Barcode barcode) async {
    return _lock.synchronized(() async {
      if (_isExecuting) {
        return null;
      }

      _isExecuting = true;

      try {
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.stop();
        }

        // Show loading indicator while connecting
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        );

        // ignore: use_build_context_synchronously
        String? msg = await DialogAction.connect
            // ignore: use_build_context_synchronously
            .action(imei: barcode.rawValue, context: context);
        
        // Dialog is closed by the connect action

        if (msg == null) {
          closePageOperation(200);
        } else {
          _isExecuting = false;
          _streams.updateMessage(msg);
          // Reset camera to continue scanning
          if (Platform.isAndroid || Platform.isIOS) {
            await controller?.start();
          }
        }
      } catch (e) {
        _isExecuting = false;
        _streams.updateMessage(e.toString());
        
        // Make sure dialog is closed in case of errors
        // ignore: use_build_context_synchronously
        if (context.mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        
        // Reset camera to continue scanning
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.start();
        }
      }

      return null;
    });
  }

  getTextFieldContainers(double width, double height, ThemeMode themeMode) {
    List<Widget> widgets = [];
    for (int i = 0; i < _codeContainerControllers.length; i++) {
      widgets.add(
        StreamBuilder<String>(
            stream: _streams.streams[i],
            initialData: "",
            builder: (context, snapshot) {
              return Container(
                width: 58 / 414 * width,
                height: 80 / 896 * height,
                decoration: BoxDecoration(
                    color: themeMode == ThemeMode.light
                        ? colorGrey200
                        : colorGrey600,
                    border: Border.all(color: colorGrey300, width: 1.4),
                    boxShadow: [
                      BoxShadow(
                          color: themeMode == ThemeMode.light
                              ? colorGrey200
                              : colorGrey600,
                          offset: const Offset(-3, -3),
                          spreadRadius: 3,
                          blurRadius: 3)
                    ],
                    borderRadius: BorderRadius.circular(8.0)),
                child: Center(
                  child: TextField(
                      style: Theme.of(context)
                          .primaryTextTheme
                          .headlineLarge
                          ?.copyWith(
                              fontSize: 48 / 414 * width,
                              fontWeight: FontWeight.w400),
                      maxLength: 1,
                      textAlign: TextAlign.center,
                      textAlignVertical: TextAlignVertical.center,
                      keyboardType: TextInputType.number,
                      controller: _codeContainerControllers[i],
                      onChanged: (value) {
                        fillContainer(value, i);
                      },
                      decoration: InputDecoration(
                        counterText: "",
                        labelStyle: poppinsTextStyle(
                            0.029 * height, colorBlack, FontWeight.w700),
                        border: InputBorder.none,
                      ),
                      focusNode: _codeContainerFocusNodes[i]),
                ),
              );
            }),
      );
      if (i + 1 < _codeContainerControllers.length) {
        widgets.add(
          SizedBox(width: 16 / 414 * width),
        );
      }
    }
    return Row(children: widgets);
  }

  void fillContainer(String number, int index) {
    _streams.eventSink.add(FillDigitEvent(digit: number, index: index));

    if (number.isEmpty && index > 0) {
      _codeContainerFocusNodes[index - 1].requestFocus();
    } else if (index + 1 != _codeContainerFocusNodes.length && number != "") {
      _codeContainerFocusNodes[index + 1].requestFocus();
    }
  }

  void closePageOperation(int statusCode) {
    if (statusCode == 200) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const Dashboard()),
      );
    } else {
      Navigator.pop(context);
    }
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    controller?.dispose();
    for (final controller in _codeContainerControllers) {
      controller.dispose();
    }
    for (final node in _codeContainerFocusNodes) {
      node.dispose();
    }
    _streams.close();
    super.dispose();
  }
}
