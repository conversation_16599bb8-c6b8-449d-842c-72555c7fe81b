import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../common/constant.dart';
import '../common/dimensions.dart';
import '../common/image_urls.dart';
import '../common/shared_preferences_keys.dart';
import '../constant/api_urls.dart';
import '../models/vehicle_info.dart';
import '../services/api_service.dart';

class Dummy extends StatefulWidget {
  final Function bodyAction;
  final List<String> imageUrls;
  const Dummy({super.key, required this.imageUrls, required this.bodyAction});

  @override
  State<Dummy> createState() => _DummyState();
}

class _DummyState extends State<Dummy> {
  int index = 0;

  late VehicleInfo vehicleInfo = VehicleInfo();
  late UserInfo userInfo = UserInfo();
  late Function bodyAction;

  @override
  void initState() {
    bodyAction = widget.bodyAction;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget =  Center(
          child:  Image.asset(
            isTwoWheels ?
            loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState == ConnectionState.done) {
          widget = SizedBox(
            height: 775 / 896 * dimensions.height,
            child: Scaffold(
                body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: InkWell(
                      onTap: () {
                        index = index == 0 ? 1 : 0;
                        setState(() {});
                      },
                      child: Image.asset(
                        this.widget.imageUrls[index],
                      ),
                    ),
                  ),
                  const SizedBox(height: 200)
                ],
              ),
            )),
          );
        }
        return widget;
      },
      future: loadData(),
    );
  }

  loadData() async {
    JsonDecoder decoder = const JsonDecoder();

    if (currentVehicleStatus == VehicleStatus.connected) {
      SharedPreferences pref = await SharedPreferences.getInstance();
      String imei = pref.getString(connectedVehicleImeiNo) ?? "";
      http.Response vehicleInfoResponse = await BackendApi.initiateGetCall(
          ApiUrls.vehicleInfo,
          params: {"imei": imei});
      Map<String, dynamic> vehicleResponse =
          decoder.convert(vehicleInfoResponse.body);

      vehicleInfo = VehicleInfo.fromJson(vehicleResponse);
    }
    http.Response userInfoResponse =
        await BackendApi.initiateGetCall(ApiUrls.userInfo);
    Map<String, dynamic> userResponse = decoder.convert(userInfoResponse.body);
    userInfo = UserInfo.fromJson(userResponse);
  }
}
