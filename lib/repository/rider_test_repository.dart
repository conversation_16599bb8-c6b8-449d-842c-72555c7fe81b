import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/rider_test_details.dart';
import 'package:nds_app/services/api_service.dart';

class VehicleTestRepository {
  Future<VehicleTestDetails> getVehicleTestDetails({String? imei}) async {
    try {
      Map<String, dynamic> params = {};
      if (imei != null && imei.isNotEmpty) {
        params['vIdVal'] = imei;
      }

      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.userVehicleTestDetails,
        params: params,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return VehicleTestDetails.fromJson(data);
      } else {
        return VehicleTestDetails.empty();
      }
    } catch (e) {
      return VehicleTestDetails.empty();
    }
  }
}
