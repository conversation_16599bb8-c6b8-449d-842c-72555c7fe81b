import 'package:location/location.dart';
import 'package:nds_app/utils/toast.dart';

Future<LocationData> determineLocation() async {
  Location location = Location();
  int maxRetries = 3;
  int currentRetry = 0;
  Duration timeoutDuration = const Duration(seconds: 8);

  // Check if location service is enabled
  bool serviceEnabled = await location.serviceEnabled();
  if (!serviceEnabled) {
    serviceEnabled = await location.requestService();
    if (!serviceEnabled) {
      String msg = 'Location services are disabled.';
      CustomToast.error(msg);
      return Future.error(msg);
    }
  }

  // Check location permissions
  PermissionStatus permissionGranted = await location.hasPermission();
  if (permissionGranted == PermissionStatus.denied) {
    permissionGranted = await location.requestPermission();
    if (permissionGranted != PermissionStatus.granted) {
      String msg = 'Location permissions are denied';
      CustomToast.error(msg);
      return Future.error(msg);
    }
  } else if (permissionGranted == PermissionStatus.deniedForever) {
    String msg = 'Location permissions are permanently denied, we cannot request permissions.';
    CustomToast.error(msg);
    return Future.error(msg);
  }

  // Set better location options
  location.changeSettings(
    accuracy: LocationAccuracy.high,
    interval: 5000, // 5 seconds
    distanceFilter: 10, // 10 meters
  );

  // Implement retry mechanism
  while (currentRetry < maxRetries) {
    try {
      // Attempt to get location with a timeout
      return await location.getLocation().timeout(
        timeoutDuration,
        onTimeout: () {
          currentRetry++;
          if (currentRetry < maxRetries) {
           // CustomToast.error('Location fetch timeout, retrying (${currentRetry}/${maxRetries})...');
            throw 'timeout'; // This will be caught by the surrounding try-catch
          } else {
           // CustomToast.error('Location fetch timed out after ${maxRetries} attempts, using default coordinates');
            return _getDefaultLocationData();
          }
        },
      );
    } catch (e) {
      // If it's our thrown timeout error, retry
      if (e == 'timeout') {
        continue;
      }

      // For other errors, check if we should retry
      currentRetry++;
      if (currentRetry < maxRetries) {
        //CustomToast.error('Error getting location, retrying (${currentRetry}/${maxRetries})...');
      } else {
       // CustomToast.error('Failed to get location after ${maxRetries} attempts: $e');
        return _getDefaultLocationData();
      }
    }
  }

  // If all retries failed
  return _getDefaultLocationData();
}

// Helper function to create default location data
LocationData _getDefaultLocationData() {
  return LocationData.fromMap({
    'latitude': 28.612894,
    'longitude': 77.229446,
    'accuracy': 0.0,
    'altitude': 0.0,
    'speed': 0.0,
    'speed_accuracy': 0.0,
    'heading': 0.0,
    'time': DateTime.now().millisecondsSinceEpoch
  });
}