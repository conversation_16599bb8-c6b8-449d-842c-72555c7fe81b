enum ActivityType {
  privacyPolicyAcceptance("PRIVACY_POLICY_ACCEPTANCE"),
  termsConditionsAcceptance("TERMS_CONDITIONS_ACCEPTANCE"),
  login("LOGIN"),
  logout("LOGOUT");

  final String requestName;

  const ActivityType(this.requestName);

  static ActivityType getActivityType(String requestName) {
    return ActivityType.values
        .firstWhere((element) => element.requestName == requestName);
  }
}
