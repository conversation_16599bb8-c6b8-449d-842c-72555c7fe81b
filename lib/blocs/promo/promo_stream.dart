import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/api_service.dart';

class PromoStream {
  static final PromoStream _singleton = PromoStream._internal();

  factory PromoStream() => _singleton;

  PromoStream._internal();

  final _profileStreamController = StreamController<List<String>>.broadcast();

  Stream<List<String>> get profileStream => _profileStreamController.stream;

  Future<void> fetchProfileDetails() async {
    try {
      const jsonDecoder = JsonDecoder();
      http.Response response = await BackendApi.initiateGetCall(
          ApiUrls.getPromotionalImage,
          params: {
            "appType": isB2CUser ? "B2C_APP" : "B2B_APP",
            "promoType": "DEFAULT",
            "org_id": organisationId,
          });
      final List<dynamic> responseBody = jsonDecoder.convert(response.body);

      List<String> imageList = [];

      for (var entry in responseBody) {
        if (entry is Map<String, dynamic> && entry.containsKey('url')) {
          String? url = entry['url'];
          if (url != null && url.isNotEmpty) {
            imageList.add(url);
          } else {
            debugPrint("Invalid or empty URL in promotional image: $entry");
          }
        } else {
          debugPrint("Unexpected entry in promotional image response: $entry");
        }
      }
      _profileStreamController.add(imageList);
    } catch (e) {
      _profileStreamController.addError('An error occurred: $e');
    }
  }

  void closeStream() {
    _profileStreamController.close();
  }
}
