import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Nds extends Company {

  @override
  String splashScreenLoadingPageCircularWhite = splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg = splashScreenImages["nds_Logo_Bg"]!;

  @override
  String splashScreenLoadingPageCompanyLogo1 =
      splashScreenImages["nds_logo_2"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
      splashScreenImages["nds_logo_2"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
      splashScreenImages["nds_logo_1"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImage"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text15']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["nds_logo_2"]!;

  @override
  Color loginThemeColor = primaryOrange;

  @override
  String contactMail = ndsContactMail;

  @override
  String contactPhoneNumber = ndsContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = ndsWebsite;

  @override
  String iosAppId = ndsB2BIosAppId;

  @override
  String androidPackageName = ndsB2BAndroidPackageName;

  @override
  int noOfWheels = ndsWheels;
}
