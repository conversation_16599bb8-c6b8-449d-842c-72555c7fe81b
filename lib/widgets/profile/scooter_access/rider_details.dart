import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_state.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';

import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/add_rider_body.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/relation.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/utils/horizontal_gesture_button.dart';
import 'package:nds_app/services/overlay_service.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/widgets/profile/edit_profile_item.dart';
import 'package:nds_app/widgets/profile/scooter_access/invite_sent.dart';
import 'package:nds_app/widgets/profile/scooter_access/relation_selection.dart';
import 'package:nds_app/widgets/profile/scooter_access/scooter_access_screen.dart';
import 'package:share_plus/share_plus.dart';
import '../../../blocs/profile/profile_details_stream.dart';
import '../../../common/image_urls.dart';
import '../../../constant/api_urls.dart';
import '../../../services/api_service.dart';
import '../../../utils/toast.dart';

class ScooterAccessRiderDetails extends StatefulWidget {
  final String? number;
  final String? name;
  final bool isEditScreen;
  final Rider? editRider;
  final BuildContext lastContext;

  const ScooterAccessRiderDetails(this.name, this.number,
      {super.key,
      required this.isEditScreen,
      this.editRider,
      required this.lastContext});
  @override
  State<ScooterAccessRiderDetails> createState() =>
      _ScooterAccessRiderDetailsState();
}

class _ScooterAccessRiderDetailsState extends State<ScooterAccessRiderDetails> {
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  bool isTextFieldEnabled = true;
  bool isTextMessageExist = false;
  bool isRiderAdded = false;
  Rider? owner;
  bool isValidPhoneNumber = false;
  final stream = ProfileStream();

  @override
  void initState() {
    _firstNameController.text = widget.name ?? "";
    _phoneNumberController.text = widget.number ?? "";
    validatePhoneNumber(_phoneNumberController.text);
    if (widget.editRider != null) {
      context
          .read<SelectRelationBloc>()
          .add(SelectRelationEvent(relation: widget.editRider!.relationType));
    }

    List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
    for (Rider rider in riders) {
      if (rider.isOwner) {
        owner = rider;
      }
    }
    stream.fetchProfileDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Scaffold(
        body: StreamBuilder<Map<String, dynamic>>(
            stream: stream.profileStream,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return  Center(child: Image.asset(
                  isTwoWheels
                      ? loaderGifImages['2Wheels']!
                      : loaderGifImages['3Wheels']!,
                ),);
              }

              Map<String, dynamic>? profile = snapshot.data;

              return Padding(
                padding: EdgeInsets.only(
                  left: 20 / 414 * dimensions.width,
                  right: 20 / 896 * dimensions.width,
                  top: 68 / 896 * dimensions.height,
                ),
                child: BlocListener<UserRiderBloc, RiderFetchState>(
                  listener: (blocListenerContext, state) {
                    if (!isRiderAdded && !widget.isEditScreen) {
                      if (state.apiStatus == ApiStatus.loading) {
                        getCircularProgressIndicator(context);
                      }
                      List<Rider> riders = state.verificationPendingRiders;
                      bool addedRider = riders.any(
                        (rider) =>
                            rider.riderPhoneNumber ==
                            _phoneNumberController.text,
                      );
                      if (state.apiStatus != ApiStatus.loading) {
                        Navigator.of(context).pop();
                      }

                      if (addedRider) {
                        setState(() {
                          isRiderAdded = true;
                        });
                        ShowOverlay.getBottomCenterOverlay(
                            // ignore: use_build_context_synchronously
                            context,
                            InviteSent(
                              vehicleColor: loginThemeColor,
                              colorType: ColorType.normal,
                              label:
                                  profileScreen["scooterAccessRiderDetails14"]!,
                            ),
                            120);
                      }
                    }
                  },
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            GestureDetector(
                              onTap: () async {
                                /*context
                                    .read<UserRiderBloc>()
                                    .add(LoadUserRidersEvent());*/
                                Navigator.of(context).pop();
                                // widget.lastContext.read<UserRiderBloc>().add(LoadUserRidersEvent());
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Icon(
                                  Icons.arrow_back_ios,
                                  size: 15 / 414 * dimensions.width,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 4 / 414 * dimensions.width,
                            ),
                            Text(
                              profileScreen[widget.isEditScreen
                                  ? "scooterAccessRiderDetailsEditTitle"
                                  : "scooterAccessRiderDetailsTitle"]!,
                              style: Theme.of(context).textTheme.headlineLarge,
                            ),
                          ],
                        ),
                        EditProfileItem(
                          title: profileScreen["scooterAccessRiderDetails1"]!,
                          hint: profileScreen["scooterAccessRiderHint1"]!,
                          controller: _firstNameController,
                          isTextFieldEnabled: isTextFieldEnabled,
                          onTap: () {},
                          keyboardType: TextInputType.name,
                          maxLength: 20,
                          isNameTextField: false,
                        ),
                        EditProfileItem(
                          title: profileScreen["scooterAccessRiderDetails2"]!,
                          hint: profileScreen["scooterAccessRiderHint2"]!,
                          controller: _phoneNumberController,
                          onTap: () {
                            setState(() {
                              isTextMessageExist = false;
                            });
                          },
                          onChanged: (value) {
                            validatePhoneNumber(value);
                          },
                          errorText: !isValidPhoneNumber &&
                                  _phoneNumberController.text.isNotEmpty
                              ? profileScreen['scooterAccessRiderMobileAlert']
                              : null,
                          keyboardType: TextInputType.phone,
                          isTextFieldEnabled: isTextFieldEnabled,
                          maxLength: 10,
                        ),
                        Visibility(
                            visible: isTextMessageExist,
                            child: displayPhoneNumberMessage(
                                profileScreen["scooterAccessRiderMessage1"]!,
                                dimensions)),
                        const RelationSelection(),
                        const Expanded(child: SizedBox()),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Visibility(
                              visible: widget.isEditScreen,
                              child: Align(
                                alignment: Alignment.bottomLeft,
                                child: HorizontalGestureButton(
                                  action: () {
                                    Navigator.of(context).pop();
                                  },
                                  label: commonText["labelCancel"]!,
                                  width: dimensions.width * 0.4,
                                  height: 54,
                                  vehicleColor: colorGrey800,
                                  textColor: colorWhite,
                                  vehicleColorType: ColorType.normal,
                                ),
                              ),
                            ),
                            Visibility(
                                visible: widget.isEditScreen,
                                child: const Spacer()),
                            Align(
                              alignment: widget.isEditScreen
                                  ? Alignment.bottomRight
                                  : Alignment.bottomLeft,
                              child:
                                  BlocBuilder<UserRiderBloc, RiderFetchState>(
                                builder: (blocContext, state) {
                                  return HorizontalGestureButton(
                                    action: widget.isEditScreen
                                        ? () async {
                                            if (_firstNameController.text == "") {
                                              CustomToast.message(toastMessageText['connectRiderButton10']!);
                                            } else if (_firstNameController.text !=
                                                    "" &&
                                                _phoneNumberController.text !=
                                                    "" &&
                                                isValidPhoneNumber) {
                                              FocusManager.instance.primaryFocus
                                                  ?.unfocus();
                                              getCircularProgressIndicator(
                                                  context);

                                              await Future.delayed(
                                                  const Duration(
                                                      milliseconds: 500));
                                              String? newNumber;
                                              if (widget.editRider!
                                                      .riderPhoneNumber !=
                                                  _phoneNumberController.text) {
                                                newNumber =
                                                    _phoneNumberController.text;
                                              }

                                              VehicleRiderBody
                                                  vehicleRiderBody =
                                                  VehicleRiderBody(
                                                      regNo: widget
                                                          .editRider!.regNo,
                                                      riderName:
                                                          _firstNameController
                                                              .text,
                                                      riderPhoneNumber:
                                                          "+91${widget.editRider!.riderPhoneNumber}",
                                                      newRiderPhoneNumber:
                                                          newNumber == null
                                                              ? null
                                                              : "+91$newNumber",
                                                      // ignore: use_build_context_synchronously
                                                      relationType: context
                                                          .read<
                                                              SelectRelationBloc>()
                                                          .state
                                                          .relation,
                                                      isEdit: true);

                                              // ignore: use_build_context_synchronously
                                              http.Response response =
                                                  await BackendApi
                                                      .initiatePutCall(
                                                ApiUrls.addUserRider,
                                                body: vehicleRiderBody.toJson(),
                                              );
                                              if (response.statusCode != 200) {
                                                String message =
                                                    'Failed to Update rider';
                                                CustomToast.error(message);
                                                throw Exception(message);
                                              }
                                              int count = 0;
                                              // ignore: use_build_context_synchronously
                                              Navigator.pushAndRemoveUntil(
                                                  // ignore: use_build_context_synchronously
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) =>
                                                          const ScooterAccessScreen()),
                                                  (r) =>
                                                      count++ >= 3).then(
                                                  (e) {});
                                              ShowOverlay.getBottomCenterOverlay(
                                                  // ignore: use_build_context_synchronously
                                                  context,
                                                  InviteSent(
                                                    vehicleColor:
                                                        loginThemeColor,
                                                    colorType: ColorType.normal,
                                                    label: profileScreen[
                                                        "scooterAccessRiderDetails16"]!,
                                                  ),
                                                  120
                                              );
                                            }
                                          }
                                        : (isRiderAdded
                                            ? () {
                                                int count = 0;
                                                // ignore: use_build_context_synchronously
                                                Navigator.pushAndRemoveUntil(
                                                    // ignore: use_build_context_synchronously
                                                    context,
                                                    MaterialPageRoute(
                                                        builder: (context) =>
                                                            const ScooterAccessScreen()),
                                                    (r) =>
                                                        count++ >= 3).then(
                                                    (e) {});
                                              }
                                            : () async {
                                                String ownerMobileNumber =
                                                    profile?["mobileNumber"] ??
                                                        "";
                                                String addedRiderNum = "+91${_phoneNumberController.text}";

                                                if(ownerMobileNumber == addedRiderNum){
                                                  isTextMessageExist = true;
                                                  CustomToast.message(toastMessageText['connectRiderButton9']!);
                                                  return;
                                                }
                                                List<Rider> riders =
                                                    state.authorizedRiders;

                                                riders.addAll(state
                                                    .verificationPendingRiders);
                                                bool exists =
                                                    riders.any((rider) {
                                                  return rider
                                                          .riderPhoneNumber ==
                                                      _phoneNumberController
                                                          .text;
                                                });

                                                if (exists) {
                                                  setState(() {
                                                    isTextMessageExist = true;
                                                  });
                                                }else if (_firstNameController.text ==
                                                    ""){
                                                  CustomToast.message(toastMessageText['connectRiderButton10']!);
                                                }
                                                else if (_phoneNumberController
                                                            .text !=
                                                        "" &&
                                                    _firstNameController.text !=
                                                        "" &&
                                                    isValidPhoneNumber) {
                                                  setState(() {
                                                    isTextMessageExist = false;
                                                    isTextFieldEnabled = false;
                                                  });
                                                  FocusManager
                                                      .instance.primaryFocus
                                                      ?.unfocus();
                                                  getCircularProgressIndicator(
                                                      context);
                                                  Relation selectedRelation =
                                                      blocContext
                                                          .read<
                                                              SelectRelationBloc>()
                                                          .state
                                                          .relation;

                                                  VehicleRiderBody
                                                      vehicleRiderBody =
                                                      VehicleRiderBody(
                                                          regNo: owner?.regNo ??
                                                              "",
                                                          riderName:
                                                              _firstNameController
                                                                  .text,
                                                          riderPhoneNumber:
                                                              "+91${_phoneNumberController.text}",
                                                          relationType:
                                                              selectedRelation,
                                                          isEdit: false);

                                                  blocContext
                                                      .read<UserRiderBloc>()
                                                      .add(AddOrUpdateUserRiderEvent(
                                                          vehicleRiderBody));
                                                  Navigator.of(context).pop();
                                                }
                                              }),
                                    label: widget.isEditScreen
                                        ? profileScreen[
                                            "scooterAccessRiderDetails8"]!
                                        : (isRiderAdded
                                            ? profileScreen[
                                                "scooterAccessRiderDetails15"]!
                                            : profileScreen[
                                                "scooterAccessRiderDetails13"]!),
                                    width: dimensions.width * 0.4,
                                    height: 54,
                                    vehicleColor: isRiderAdded
                                        ? colorGrey800
                                        : colorGreenSuccess,
                                    textColor: colorWhite,
                                    vehicleColorType: ColorType.normal,
                                  );
                                },
                              ),
                            ),
                            Visibility(
                                visible: !widget.isEditScreen,
                                child: const Spacer()),
                            Visibility(
                              visible: !widget.isEditScreen,
                              child: Align(
                                alignment: Alignment.bottomRight,
                                child: HorizontalGestureButton(
                                  action: isRiderAdded
                                      ? () async {
                                          String? ownerName =
                                              userInfo!.firstName;
                                          String? riderPhone =
                                              _phoneNumberController.text;
                                          String androidId = androidPackageName;
                                          String iosId = iosAppId;
                                          String message =
                                              sharedRiderDetailsMessageFormat;

                                          message = message.replaceFirst(
                                              "@ownerName", ownerName ?? "");
                                          message = message.replaceFirst(
                                              "@vehicleRegNo",
                                              owner?.regNo ?? "");
                                          message = message.replaceFirst(
                                              "@riderPhoneNumber", riderPhone);
                                          message = message.replaceFirst(
                                              "@androidPackageName", androidId);
                                          message = message.replaceFirst(
                                              "@iosAppId", iosId);
                                          Share.share(message);
                                        }
                                      : () {},
                                  label: profileScreen[
                                      "scooterAccessRiderDetails9"]!,
                                  width: dimensions.width * 0.4,
                                  height: 54,
                                  vehicleColor: isRiderAdded
                                      ? colorGreenSuccess
                                      : colorGrey400,
                                  textColor: colorWhite,
                                  vehicleColorType: ColorType.normal,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 24 / 896 * dimensions.height,
                        )
                      ]),
                ),
              );
            }));
  }

  displayPhoneNumberMessage(String message, Dimensions dimensions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: 8 / 896 * dimensions.width,
        ),
        Align(
          alignment: Alignment.topLeft,
          child: Text(message,
              style: poppinsTextStyle(
                  12 / 414 * dimensions.width, colorRed, FontWeight.w200)),
        ),
      ],
    );
  }

  void validatePhoneNumber(String value) {
    setState(() {
      // Remove any non-digit characters
      String digitsOnly = value.replaceAll(RegExp(r'[^0-9]'), '');

      // Ensure exactly 10 digits and starts with valid prefix
      isValidPhoneNumber = digitsOnly.length == 10 &&
          (digitsOnly.startsWith('6') ||
              digitsOnly.startsWith('7') ||
              digitsOnly.startsWith('8') ||
              digitsOnly.startsWith('9'));
    });
  }
}
