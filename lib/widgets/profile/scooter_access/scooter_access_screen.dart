import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_state.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/verification_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/utils/custom_switch.dart';
import 'package:nds_app/utils/horizontal_gesture_button.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/profile/scooter_access/bottom_sheet_add_rider.dart';
import 'package:nds_app/widgets/profile/scooter_access/rider_widget.dart';
import '../../../common/constant.dart';

class ScooterAccessScreen extends StatefulWidget {
  const ScooterAccessScreen({
    super.key,
  });

  @override
  State<ScooterAccessScreen> createState() => _ScooterAccessScreenState();
}

class _ScooterAccessScreenState extends State<ScooterAccessScreen> {
  List<Rider> verificationPendingRiders = [];
  @override
  void initState() {
    context.read<UserRiderBloc>().add(LoadUserRidersEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    final GlobalKey overlayMenuButtonKey = GlobalKey();
    return GestureDetector(
      onTap: () {
        OverlayEntry? entry =
            context.read<EditRiderDropDownBloc>().state.overlayEntry;
        if (entry != null) {
          entry.remove();
          entry = null;
          context
              .read<EditRiderDropDownBloc>()
              .add(const EditRiderDropDownEvent(overlayEntry: null));
        }
      },
      child: Scaffold(
        floatingActionButton:     Padding(
          padding: const EdgeInsets.only(left: 24),
          child: HorizontalGestureButton(
            action: () {
              List<Rider> riders =
                  context.read<UserVehicleBloc>().state.riders;

              Rider? owner;
              for (var e in riders) {
                if (e.isOwner) {
                  owner = e;
                }
              }

              if (owner != null) {
                OverlayEntry? entry =
                    context.read<EditRiderDropDownBloc>().state.overlayEntry;
                if (entry != null) {
                  entry.remove();
                  entry = null;
                  context
                      .read<EditRiderDropDownBloc>()
                      .add(const EditRiderDropDownEvent(overlayEntry: null));
                }
                getBottomSheetAddRider(
                    parentContext: context, vehicleColor: loginThemeColor);
              } else {
                CustomToast.message(toastMessageText["connectRiderButton5"]!,
                    gravity: ToastGravity.TOP);
              }
            },

            label: 'Add Rider',
            width: dimensions.width*1,
            height: 56,
            vehicleColor: loginThemeColor,
            textColor: colorWhite,
            vehicleColorType: ColorType.dark,
          ),
        ),
          body: Padding(
        padding: EdgeInsets.only(
          left: 20 / 414 * dimensions.width,
          right: 20 / 414 * dimensions.width,
          top: 68 / 896 * dimensions.height,
        ),

        child: SingleChildScrollView(
          child: Column(children: [
            Row(
              children: [
                GestureDetector(
                  onTap: () async {
                    Navigator.of(context).pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: 15 / 414 * dimensions.width,
                    ),
                  ),
                ),
                SizedBox(
                  width: 4 / 414 * dimensions.width,
                ),
                Text(
                  profileScreen["menu7"]!,
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
              ],
            ),
            BlocBuilder<UserRiderBloc, RiderFetchState>(
              builder: (context, state) {
                if (state.apiStatus == ApiStatus.loading) {
                  return SizedBox(
                      height: 680 / 896 * dimensions.height,
                      child:  Center(child: Image.asset(
                        isTwoWheels
                            ? loaderGifImages['2Wheels']!
                            : loaderGifImages['3Wheels']!,
                      ),));
                } else if (state.apiStatus == ApiStatus.success) {
                  final authorizedRiders = state.authorizedRiders;
                  final verificationPendingRiders =
                      state.verificationPendingRiders;
          
                  Widget widget = noScooterAccessRiders(dimensions);
                  if (authorizedRiders.isNotEmpty ||
                      verificationPendingRiders.isNotEmpty) {
                    widget = Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 28 / 896 * dimensions.height,
                        ),
                        if (authorizedRiders.isNotEmpty) ...[
                          Text(
                            profileScreen["scooterAccess3"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          SizedBox(height: 8 / 896 * dimensions.height),
                          ...getRiderItemFromRider(
                              authorizedRiders, dimensions, overlayMenuButtonKey),
                          SizedBox(height: 32 / 896 * dimensions.height),
                        ],
                        if (verificationPendingRiders.isNotEmpty) ...[
                          Text(
                            profileScreen["scooterAccess4"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          SizedBox(height: 8 / 896 * dimensions.height),
                          ...getRiderItemFromRider(verificationPendingRiders,
                              dimensions, overlayMenuButtonKey),
                        ],
                      ],
                    );
                  }
                  return widget;
                } else {
                  return Center(
                      child: Text(
                    'No data available',
                    style: Theme.of(context).textTheme.labelLarge,
                  ));
                }
              },
            ),
           const SizedBox(
              height: 100,
            ),
          ]),
        ),
      )),
    );
  }

  Widget noScooterAccessRiders(Dimensions dimensions) {
    return SizedBox(
      height: 667 / 896 * dimensions.height,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 94 / 414 * dimensions.width,
            height: 94 / 896 * dimensions.height,
            child: Image.asset(
              profileScreenImages["addRider"]!,
              color: MyApp.of(context).getCurrentThemeMode() == ThemeMode.light
                  ? colorGrey700
                  : colorGrey25,
            ),
          ),
          SizedBox(
            height: 20 / 896 * dimensions.height,
          ),
          Text(
            profileScreen["scooterAccess1"]!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(
            height: 4 / 896 * dimensions.height,
          ),
          SizedBox(
            width: 257 / 414 * dimensions.width,
            child: Text(
              profileScreen["scooterAccess2"]!,
              style: Theme.of(context).textTheme.bodySmall,
              overflow: TextOverflow.clip,
              textAlign: TextAlign.center,
            ),
          )
        ],
      ),
    );
  }

  List<RiderItem> getRiderItemFromRider(List<Rider> riders,
      Dimensions dimensions, GlobalKey overlayMenuButtonKey) {
    return riders.asMap().entries.map((entry) {
      final int index = entry.key;
      final rider = entry.value;
      final GlobalKey key = GlobalKey();

      return RiderItem(
        rider: rider,
        index: index,
        iconColor: colorGrey400,
        subWidget: rider.verificationStatus == VerificationStatus.rejected
            ? Text(
          profileScreen["rejected"]!,
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(fontSize: 12 / 414 * dimensions.width),
        )
            : rider.verificationStatus == VerificationStatus.pending
            ? Text(
          profileScreen["scooterAccess5"]!,
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(fontSize: 12 / 414 * dimensions.width),
        )
            : CustomSwitch(rider: rider),
        overlayMenuButtonKey: key,
      );
    }).toList();
  }
}
