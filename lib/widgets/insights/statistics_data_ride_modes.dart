import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_event.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/utils/convax_decoration.dart';
import 'package:nds_app/utils/extension.dart';

class StatisticsDataRideModes extends StatefulWidget {
  final String labelName;
  final Map<String, double>? ridingModesInfo;
  final String iconPath;
  final Color iconColor;
  final Color iconBgColor;
  final Color containerColor;
  final List<Color> containerBorderColor;
  final Function action;
  final bool isStaticDetailsData;

  const StatisticsDataRideModes(
      {super.key,
      required this.labelName,
      required this.iconPath,
      required this.iconColor,
      required this.iconBgColor,
      required this.containerColor,
      required this.containerBorderColor,
      required this.action,
      required this.ridingModesInfo,
      required this.isStaticDetailsData});

  @override
  State<StatisticsDataRideModes> createState() =>
      _StatisticsDataRideModesState();
}

class _StatisticsDataRideModesState extends State<StatisticsDataRideModes> {
  int avgMixRange = 0;
  Map<RidingModes, double>? ridingModes = {};
  @override
  void initState() {
    for (var entry in widget.ridingModesInfo!.entries) {
      if (entry.value != 0) {
        RidingModes mode = RidingModes.values
            .firstWhere((e) => e.name == entry.key.toLowerCase());
        ridingModes?[mode] = entry.value;
      }
    }
    var entries = ridingModes!.entries.toList();
    entries.sort((a, b) => b.key.index.compareTo(a.key.index));
    ridingModes = {for (var entry in entries) entry.key: entry.value};
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocBuilder<StatisticsBloc, StatisticsState>(
      builder: (context, state) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            context.read<StatisticsBloc>().add(LoadStatistics(
                startTime: state.startTime, endTime: state.endTime));
          },
          child: InkWell(
            onTap: () {
              widget.action.call();
            },
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  height: 197 / 896 * dimensions.height,
                  width: 370 / 414 * dimensions.width,
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(30 / 414 * dimensions.width),
                    boxShadow: [
                      BoxShadow(
                        color: MyApp.of(context).getCurrentThemeMode() ==
                                ThemeMode.light
                            ? Colors.black.withOpacity(0.25)
                            : Colors.white.withOpacity(0.25),
                        spreadRadius: 0,
                        blurRadius: 2 / 414 * dimensions.width,
                        offset: Offset(-3 / 414 * dimensions.width,
                            3 / 414 * dimensions.width),
                      ),
                      BoxShadow(
                        color: widget.containerColor,
                        spreadRadius: 3 / 414 * dimensions.width,
                        blurRadius: 2 / 414 * dimensions.width,
                        offset: Offset(
                          3 / 414 * dimensions.width,
                          -3 / 414 * dimensions.width,
                        ),
                      )
                    ],
                    gradient: LinearGradient(
                      begin: AlignmentDirectional.bottomStart,
                      end: AlignmentDirectional.topEnd,
                      colors: widget.containerBorderColor,
                    ),
                  ),
                ),
                Container(
                  height: 197 / 896 * dimensions.height,
                  width: 370 / 414 * dimensions.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                    border: Border.all(
                      width: 1 / 414 * dimensions.width,
                      color: Colors.transparent,
                    ),
                    color: widget.iconBgColor,
                  ),
                ),
                Container(
                  height: 197 / 896 * dimensions.height,
                  width: 370 / 414 * dimensions.width,
                  margin: EdgeInsets.only(
                      left: 4 / 414 * dimensions.width,
                      bottom: 4 / 896 * dimensions.height),
                  decoration: BoxDecoration(
                    color: widget.containerColor,
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                    gradient: LinearGradient(
                      begin: AlignmentDirectional.bottomStart,
                      end: AlignmentDirectional.topEnd,
                      colors: widget.containerBorderColor,
                    ),
                  ),
                ),
                Container(
                  height: 196 / 896 * dimensions.height,
                  width: 370 / 414 * dimensions.width,
                  margin: EdgeInsets.only(
                      left: 3 / 414 * dimensions.width,
                      bottom: 3 / 896 * dimensions.height),
                  decoration: BoxDecoration(
                    color: widget.containerColor,
                    borderRadius: BorderRadius.circular(
                      30 / 414 * dimensions.width,
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 8.0 / 896 * dimensions.height,
                        right: 8.0 / 414 * dimensions.width,
                        left: 16.0 / 414 * dimensions.width,
                        bottom: 16.0 / 896 * dimensions.height),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              height: 60 / 896 * dimensions.height,
                              width: 200 / 414 * dimensions.width,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 16 / 896 * dimensions.height,
                                  ),
                                  Text(
                                    widget.labelName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium,
                                  ),
                                  SizedBox(
                                    height: 10 / 414 * dimensions.width,
                                  ),
                                ],
                              ),
                            ),
                            Visibility(
                              visible: !widget.isStaticDetailsData,
                              child: Stack(
                                children: [
                                  Container(
                                    width: 50 / 414 * dimensions.width,
                                    height: 50 / 414 * dimensions.width,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: widget.iconBgColor,
                                    ),
                                  ),
                                  Container(
                                      width: 50 / 414 * dimensions.width,
                                      height: 50 / 414 * dimensions.width,
                                      decoration: ConvaxDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(25),
                                        ),
                                        depth: 3,
                                        colors: [
                                          Colors.black.withOpacity(0.5),
                                          colorGrey400.withOpacity(0.5)
                                        ],
                                        opacity: 0.7,
                                      ),
                                      child: Center(
                                        child: SizedBox(
                                            width: 20 / 414 * dimensions.width,
                                            height: 20 / 414 * dimensions.width,
                                            child: Image.asset(
                                              widget.iconPath,
                                              fit: BoxFit.fill,
                                            )),
                                      )),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Flexible(child: displayRideModesData(dimensions)),
                        SizedBox(
                          height: 18 / 414 * dimensions.width,
                        ),
                        Flexible(
                          child: Stack(
                            alignment: Alignment.centerRight,
                            children: [
                              Visibility(
                                visible: !widget.isStaticDetailsData,
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      right: 14 / 414 * dimensions.width),
                                  child: Align(
                                    alignment: Alignment.bottomRight,
                                    child: InkWell(
                                      onTap: (() {}),
                                      child: Container(
                                          margin: EdgeInsets.only(
                                              right:
                                                  8 / 414 * dimensions.width),
                                          width: 15 / 414 * dimensions.width,
                                          height: 15 / 896 * dimensions.height,
                                          child: Image.asset(
                                            insightsScreenImages[
                                                'expand_widget_icon']!,
                                            color: MyApp.of(context)
                                                        .getCurrentThemeMode() ==
                                                    ThemeMode.dark
                                                ? colorGrey25
                                                : colorBlack800,
                                          )),
                                    ),
                                  ),
                                ),
                              ),
                              Center(
                                child: RichText(
                                    text: TextSpan(children: [
                                  TextSpan(
                                    text: insightsText['text15']!,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(
                                            fontSize:
                                                30 / 414 * dimensions.width),
                                  ),
                                  TextSpan(
                                    text: avgMixRange.toString(),
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  TextSpan(
                                    text: insightsText['text10']!,
                                    style:
                                        Theme.of(context).textTheme.bodyLarge,
                                  )
                                ])),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget displayRideModesData(Dimensions dimensions) {
    List<Widget> list = [];

    for (var entry in ridingModes!.entries) {
      int avgRange = entry.value.round();
      avgMixRange += avgRange;
      list.add(Text(
        entry.key.name.substring(0, 1).toUpperCase(),
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: 30 / 414 * dimensions.width,
            color: entry.key.color.toColor(),
            height: 1.2),
      ));
      list.add(SizedBox(
        width: 4 / 414 * dimensions.width,
      ));
      list.add(Text(
        avgRange.toString(),
        style: Theme.of(context).textTheme.titleLarge?.copyWith(height: 1),
      ));

      list.add(Text(
        insightsText['text10']!,
        style: Theme.of(context).textTheme.bodyLarge,
      ));
      list.add(SizedBox(
        width: 16 / 414 * dimensions.width,
      ));
    }
    if (ridingModes!.isNotEmpty) {
      avgMixRange = (avgMixRange / ridingModes!.length).round();
    }

    return SizedBox(
        width: 360 / 414 * dimensions.width,
        height: 40 / 896 * dimensions.height,
        child: FittedBox(
          child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: list),
        ));
  }
}
