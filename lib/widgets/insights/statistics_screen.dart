import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_details_event.dart';
import 'package:nds_app/blocs/insight/statistics/load_statistics_event.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/drive_modes_info.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/models/statistics.dart';
import 'package:nds_app/models/statistics_container_data.dart';
import 'package:nds_app/models/statistics_details.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/insights/statistics_data_ride_modes.dart';
import 'package:nds_app/widgets/insights/statistics_data_square_container.dart';
import 'package:nds_app/widgets/insights/statistics_date_time_picker.dart';
import 'package:nds_app/widgets/insights/statistics_details_chart.dart';
import 'package:nds_app/widgets/insights/statistics_mode_range_chart.dart';

class StatisticsScreen extends StatefulWidget {
  final Color color;
  final ColorType colorType;
  const StatisticsScreen(
      {super.key, required this.color, required this.colorType});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  late List<RidingModesInfo> ridingModesInfo;
  late Color color;
  late ColorType colorType;

  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    final staticBloc = context.read<StatisticsBloc>();
    final currentState = staticBloc.state;
    context.read<StatisticsBloc>().add(LoadStatistics(
        startTime: currentState.startTime, endTime: currentState.endTime));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    color = currentVehicleStatus == VehicleStatus.connected
        ? (colorType == ColorType.light
            ? Theme.of(context).highlightColor
            : color)
        : loginThemeColor;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: isProdRedUSer,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(insightsText["text16"]!,
                  style: Theme.of(context).textTheme.headlineLarge,
                  textAlign: TextAlign.left,
                  ),
            )
        ),
        Column(
          children: [
            BlocBuilder<StatisticsBloc, StatisticsState>(
              builder: (context, state) {
                return Visibility(
                  visible:
                      state.statisticsDataType != StatisticsDataType.statistics,
                  child: InkWell(
                    onTap: () async {
                      context.read<StatisticsBloc>().add(LoadStatistics(
                          startTime: state.startTime, endTime: state.endTime));
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 24 / 414 * dimensions.width,
                          vertical: 16 / 896 * dimensions.height),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.arrow_back_ios,
                            size: 15,
                          ),
                          SizedBox(
                            width: 8 / 414 * dimensions.width,
                          ),
                          Text(
                            commonText["button1"]!,
                            style: Theme.of(context).textTheme.headlineLarge,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            SizedBox(
              height: 48 / 896 * dimensions.height,
              child: const StatisticsDateTimePicker(),
            ),
            Container(
              height: 610 / 896 * dimensions.height,
              color: Theme.of(context).scaffoldBackgroundColor,
              padding:
                  EdgeInsets.symmetric(horizontal: 12 / 414 * dimensions.width),
              child: BlocBuilder<StatisticsBloc, StatisticsState>(
                builder: (context, state) {
                  Widget widget =  Center(child: Image.asset(
                    isTwoWheels
                        ? loaderGifImages['2Wheels']!
                        : loaderGifImages['3Wheels']!,
                  ),);
                  switch (state.apiStatus) {
                    case ApiStatus.success:
                      if (state.statisticsDataType ==
                          StatisticsDataType.statistics) {
                        widget = SizedBox(
                          height: 610 / 896 * dimensions.height,
                          child: SingleChildScrollView(
                            child: Column(children: [
                              SizedBox(
                                height: 20 / 896 * dimensions.height,
                              ),
                              SizedBox(
                                height: 600 / 896 * dimensions.height,
                                child: GridView.count(
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: 2,
                                  children: List.generate(6, (index) {
                                    StatisticsContainerData
                                        statisticsContainerData =
                                        getStatisticsContainerData(index, state);
                                    return StatisticsDataSquareContainer(
                                      statisticsDataContainerImagePath:
                                          statisticsContainerData.imagePath,
                                      isStaticDetailsData: false,
                                      labelName: statisticsContainerData.labelName,
                                      data: statisticsContainerData.data,
                                      unit: statisticsContainerData.unit,
                                      iconPath: statisticsContainerData.iconPath,
                                      iconColor: colorWhite,
                                      iconBgColor: color,
                                      containerColor:
                                          Theme.of(context).scaffoldBackgroundColor,
                                      containerBorderColor:
                                          MyApp.of(context).getCurrentThemeMode() ==
                                                  ThemeMode.light
                                              ? const [
                                                  colorWhite,
                                                  colorStatisticsContainerLight
                                                ]
                                              : const [
                                                  colorStatisticsContainerBlack,
                                                  colorBlack
                                                ],
                                      action: statisticsContainerData.action,
                                    );
                                  }),
                                ),
                              ),
                              SizedBox(
                                height: 20 / 896 * dimensions.height,
                              ),
                              StatisticsDataRideModes(
                                  isStaticDetailsData: false,
                                  labelName: insightsText['text9']!,
                                  ridingModesInfo:
                                      state.statistics.avgDriveModesRange,
                                  iconPath: insightsScreenImages['curve_arrow']!,
                                  iconColor: colorWhite,
                                  iconBgColor: color,
                                  containerColor:
                                      Theme.of(context).scaffoldBackgroundColor,
                                  containerBorderColor:
                                      MyApp.of(context).getCurrentThemeMode() ==
                                              ThemeMode.light
                                          ? const [
                                              colorWhite,
                                              colorStatisticsContainerLight
                                            ]
                                          : const [
                                              colorStatisticsContainerBlack,
                                              colorBlack
                                            ],
                                  action: () {
                                    context.read<StatisticsBloc>().add(
                                        LoadStatisticsDetails(
                                            StatisticsDataType.modeRange,
                                            startTime: state.startTime,
                                            endTime: state.endTime));
                                  }),
                              SizedBox(
                                height: 120 / 896 * dimensions.height,
                              )
                            ]),
                          ),
                        );
                      } else {
                        StatisticsContainerData statisticsContainerData =
                            getStatisticsContainerData(0, state);
                        widget = Column(
                          children: [
                            SizedBox(
                              height: 20 / 896 * dimensions.height,
                            ),
                            Visibility(
                              visible: state.statisticsDataType !=
                                  StatisticsDataType.modeRange,
                              child: StatisticsDataSquareContainer(
                                statisticsDataContainerImagePath:
                                    statisticsContainerData.imagePath,
                                isStaticDetailsData: true,
                                labelName: statisticsContainerData.labelName,
                                data: statisticsContainerData.data,
                                unit: statisticsContainerData.unit,
                                iconPath: statisticsContainerData.iconPath,
                                iconColor: colorWhite,
                                iconBgColor: color,
                                containerColor:
                                    Theme.of(context).scaffoldBackgroundColor,
                                containerBorderColor:
                                    MyApp.of(context).getCurrentThemeMode() ==
                                            ThemeMode.light
                                        ? const [
                                            colorWhite,
                                            colorStatisticsContainerLight
                                          ]
                                        : const [
                                            colorStatisticsContainerBlack,
                                            colorBlack
                                          ],
                                action: statisticsContainerData.action,
                              ),
                            ),
                            Visibility(
                              visible: state.statisticsDataType ==
                                  StatisticsDataType.modeRange,
                              child: StatisticsDataRideModes(
                                  isStaticDetailsData: true,
                                  labelName: insightsText['text9']!,
                                  ridingModesInfo:
                                      state.statisticsDetails.avgModeRange,
                                  iconPath: insightsScreenImages['curve_arrow']!,
                                  iconColor: colorWhite,
                                  iconBgColor: color,
                                  containerColor:
                                      Theme.of(context).scaffoldBackgroundColor,
                                  containerBorderColor:
                                      MyApp.of(context).getCurrentThemeMode() ==
                                              ThemeMode.light
                                          ? const [
                                              colorWhite,
                                              colorStatisticsContainerLight
                                            ]
                                          : const [
                                              colorStatisticsContainerBlack,
                                              colorBlack
                                            ],
                                  action: () {}),
                            ),
                            SizedBox(
                              height: 20 / 896 * dimensions.height,
                            ),
                            Visibility(
                                visible: state.statisticsDataType !=
                                    StatisticsDataType.modeRange,
                                child: const StatisticsDetailsChart()),
                            Visibility(
                                visible: state.statisticsDataType ==
                                    StatisticsDataType.modeRange,
                                child: const StatisticsModeRangeChart())
                          ],
                        );
                      }

                      break;

                    case ApiStatus.loading:
                      widget =  Center(child: Image.asset(
                        isTwoWheels
                            ? loaderGifImages['2Wheels']!
                            : loaderGifImages['3Wheels']!,
                      ),);
                      break;
                    case ApiStatus.failure:
                      //CustomToast.error("Failed to fetch statistics data");
                      break;
                  }
                  return widget;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  StatisticsContainerData getStatisticsContainerData(
      int index, StatisticsState state) {
    StatisticsContainerData statisticsContainerData = StatisticsContainerData(
        labelName: '',
        data: [],
        unit: [],
        iconPath: '',
        action: () {},
        imagePath: '');
    StatisticsDataType statisticsDataType = state.statisticsDataType;
    if (state.statisticsDataType == StatisticsDataType.statistics) {
      Statistics statistics = state.statistics;

      switch (index) {
        case 0:
          statisticsContainerData.labelName = insightsText['text3']!;
          statisticsContainerData.data = [
            statistics.distanceTravelled == null
                ? "0"
                : statistics.distanceTravelled.toString()
          ];
          statisticsContainerData.unit = [insightsText['text10']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['distance_traveled']!;
          statisticsDataType = StatisticsDataType.distanceTravelled;
          break;
        case 1:
          statisticsContainerData.labelName = insightsText['text4']!;
          statisticsContainerData.data =
              getTimeInHourAndMinFromMins(statistics.rideDuration);
          statisticsContainerData.unit = [
            insightsText['text11']!,
            insightsText['text12']!
          ];
          statisticsContainerData.iconPath =
              insightsScreenImages['time_forward']!;
          statisticsDataType = StatisticsDataType.rideDuration;
          break;
        case 2:
          statisticsContainerData.labelName = insightsText['text5']!;
          statisticsContainerData.data = [
            statistics.avgSpeed == null
                ? "0"
                : statistics.avgSpeed!.round().toString()
          ];
          statisticsContainerData.unit = [insightsText['text13']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['tachometer_alt_slowest']!;
          statisticsDataType = StatisticsDataType.avgSpeed;
          break;
        case 3:
          statisticsContainerData.labelName = insightsText['text6']!;
          statisticsContainerData.data = [
            statistics.topSpeed == null
                ? "0"
                : statistics.topSpeed!.round().toString()
          ];
          statisticsContainerData.unit = [insightsText['text13']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['tachometer_alt_fastest']!;
          statisticsDataType = StatisticsDataType.maxSpeed;
          break;
        case 4:
          statisticsContainerData.labelName = insightsText['text7']!;
          statisticsContainerData.data =
              getTimeInHourAndMinFromMins(statistics.chargingTime);
          statisticsContainerData.unit = [
            insightsText['text11']!,
            insightsText['text12']!
          ];
          statisticsContainerData.iconPath =
              insightsScreenImages['battery_bolt']!;
          statisticsDataType = StatisticsDataType.chargingTime;
          break;
        case 5:
          statisticsContainerData.labelName = insightsText['text8']!;
          statisticsContainerData.data = ["_"];
          statisticsContainerData.unit = [insightsText['text14']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['battery_swap']!;
          statisticsDataType = StatisticsDataType.numberOfSwaps;
          break;
      }
      statisticsContainerData.action = () async {
        if (statisticsDataType != StatisticsDataType.numberOfSwaps) {
          context.read<StatisticsBloc>().add(LoadStatisticsDetails(
              statisticsDataType,
              startTime: state.startTime,
              endTime: state.endTime));
        } else {
          CustomToast.message("Details of no. of swaps is not available");
        }
      };
    } else {
      StatisticsDetails statisticsDetails = state.statisticsDetails;
      switch (statisticsDataType) {
        case StatisticsDataType.distanceTravelled:
          statisticsContainerData.labelName = insightsText['text3']!;
          statisticsContainerData.data = [
            statisticsDetails.value == null
                ? "0"
                : statisticsDetails.value.round().toString()
          ];
          statisticsContainerData.unit = [insightsText['text10']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['distance_traveled']!;
          statisticsDataType = StatisticsDataType.distanceTravelled;
          statisticsContainerData.imagePath =
              insightsScreenImages['distance_travelled_image']!;
          break;
        case StatisticsDataType.rideDuration:
          statisticsContainerData.labelName = insightsText['text4']!;
          statisticsContainerData.data =
              getTimeInHourAndMinFromMins(statisticsDetails.value?.round());
          statisticsContainerData.unit = [
            insightsText['text11']!,
            insightsText['text12']!
          ];
          statisticsContainerData.iconPath =
              insightsScreenImages['time_forward']!;
          statisticsDataType = StatisticsDataType.rideDuration;
          statisticsContainerData.imagePath =
              insightsScreenImages['ride_duration_image']!;
          break;
        case StatisticsDataType.avgSpeed:
          statisticsContainerData.labelName = insightsText['text5']!;
          statisticsContainerData.data = [
            statisticsDetails.value == null
                ? "0"
                : statisticsDetails.value!.round().toString()
          ];
          statisticsContainerData.unit = [insightsText['text13']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['tachometer_alt_slowest']!;
          statisticsDataType = StatisticsDataType.avgSpeed;
          break;
        case StatisticsDataType.maxSpeed:
          statisticsContainerData.labelName = insightsText['text6']!;
          statisticsContainerData.data = [
            statisticsDetails.value == null
                ? "0"
                : statisticsDetails.value!.round().toString()
          ];
          statisticsContainerData.unit = [insightsText['text13']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['tachometer_alt_fastest']!;
          statisticsDataType = StatisticsDataType.maxSpeed;
          break;
        case StatisticsDataType.chargingTime:
          statisticsContainerData.labelName = insightsText['text7']!;
          statisticsContainerData.data =
              getTimeInHourAndMinFromMins(statisticsDetails.value?.round());
          statisticsContainerData.unit = [
            insightsText['text11']!,
            insightsText['text12']!
          ];
          statisticsContainerData.iconPath =
              insightsScreenImages['battery_bolt']!;
          statisticsDataType = StatisticsDataType.chargingTime;
          break;
        case StatisticsDataType.numberOfSwaps:
          statisticsContainerData.labelName = insightsText['text8']!;
          statisticsContainerData.data = ["_"];
          statisticsContainerData.unit = [insightsText['text14']!];
          statisticsContainerData.iconPath =
              insightsScreenImages['battery_swap']!;
          statisticsDataType = StatisticsDataType.statistics;
          break;
        case StatisticsDataType.modeRange:
          break;
        case StatisticsDataType.statistics:
          break;
      }
    }

    return statisticsContainerData;
  }

  List<String> getTimeInHourAndMinFromMins(int? min) {
    String hours = '0';
    String mins = '0';
    if (min != null) {
      double hour = min / 60;
      int truncHour = hour.truncate();
      hours = truncHour.toString();
      mins = ((hour - truncHour) * 60).truncate().toString();
    }
    return [hours, mins];
  }
}
