import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/models/rider_test_details.dart';
import 'package:nds_app/repository/rider_test_repository.dart';
import 'package:nds_app/services/bluetooth_service.dart';
import '../../common/colors.dart';
import '../../common/image_urls.dart';
import '../../main.dart';

Widget getFuelSavingAndTotalDistanceRow(
    Dimensions dimensions,
    bool isConnected,
    int savedAmount,
    int totalDistance,
    String distanceUnit,
    BuildContext context) {
  ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();

  String formattedSavedAmount =
      NumberFormat.decimalPattern('en_IN').format(savedAmount);

  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 6 / 414 * dimensions.width),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
            visible: isConnected == true,
            child: Row(
              children: [
                Text(
                  homeScreenText["text5"]!,
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                SizedBox(
                  width: 8 / 896 * dimensions.width,
                ),
                SizedBox(
                    width: 10 / 414 * dimensions.width,
                    height: 10 / 896 * dimensions.height,
                    child: Image.asset(
                      alignment: Alignment.center,
                      homeScreenImages["rupee_icon"]!,
                      color: themeMode == ThemeMode.dark
                          ? colorGrey200
                          : colorGrey600,
                    )),
                Text(
                  formattedSavedAmount,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(
                  width: 4 / 414 * dimensions.width,
                ),
              ],
            )),
        Visibility(
          visible: isConnected,
          child: Tooltip(
            decoration: BoxDecoration(
                color: Theme.of(context).dividerColor.withOpacity(0.8),
                borderRadius:
                    BorderRadius.circular(4 / 414 * dimensions.width)),
            preferBelow: false,
            margin: EdgeInsets.only(left: 150 / 414 * dimensions.width),
            triggerMode: TooltipTriggerMode.tap,
            verticalOffset: 8 / 896 * dimensions.height,
            message: clusterScreenText["range"]!,
            textStyle: Theme.of(context)
                .textTheme
                .labelMedium
                ?.copyWith(color: colorWhite),
            child: Icon(
              Icons.info_outline,
              color: Theme.of(context).textTheme.labelSmall?.color,
              size: 12 / 414 * dimensions.width,
            ),
          ),
        ),
        Visibility(
          visible: isConnected,
          child: SizedBox(
            width: 41 / 414 * dimensions.width,
          ),
        ),
        Flexible(
          child: Text(
            homeScreenText["text1"]!,
            style: Theme.of(context).textTheme.labelMedium,
          ),
        ),
        SizedBox(
          width: 4 / 414 * dimensions.width,
        ),
        SizedBox(
          width: 14 / 414 * dimensions.width,
          height: 14 / 896 * dimensions.height,
          child: Image.asset(homeScreenImages['distance_icon']!),
        ),
        SizedBox(
          width: 8 / 414 * dimensions.width,
        ),
        SizedBox(
          width: 14 / 896 * dimensions.width,
        ),
        Flexible(
          child: Text(
            "$totalDistance $distanceUnit",
            style: Theme.of(context).textTheme.labelSmall,
          ),
        ),
      ],
    ),
  );
}

getFuelSavingDetailsContainer(
    Function action,
    Color color,
    Dimensions dimensions,
    int saved,
    int equivalent,
    int spent,
    BuildContext context) {
  String formattedSavedAmount =
      NumberFormat.decimalPattern('en_IN').format(saved);
  String formattedEquivalent =
      NumberFormat.decimalPattern('en_IN').format(equivalent);
  String formattedSpent = NumberFormat.decimalPattern('en_IN').format(spent);

  return Container(
    padding: EdgeInsets.symmetric(
      horizontal: 12 / 414 * dimensions.width,
    ),
    height: 180 / 896 * dimensions.height,
    //width: 374 / 414 * dimensions.width,
    margin: EdgeInsets.only(
        bottom: 16,
        left: 6 / 414 * dimensions.width,
        right: 6 / 414 * dimensions.width),
    decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(width: 1, color: colorGrey200),
        boxShadow: [
          BoxShadow(
              color: colorBlack.withOpacity(0.25),
              offset: const Offset(1, 3),
              blurRadius: 3,
              spreadRadius: 1),
          BoxShadow(
              color: colorWhite.withOpacity(0.25),
              offset: const Offset(-1, -3),
              blurRadius: 3,
              spreadRadius: 1)
        ]),
    child: Row(
      children: [
        FittedBox(
          child: SizedBox(
            width: 174 / 414 * dimensions.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 16 / 896 * dimensions.height,
                ),
                Row(
                  children: [
                    Text(
                      homeScreenText["text5"]!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(
                      width: 5 / 414 * dimensions.width,
                    ),
                    SizedBox(
                        height: 20 / 896 * dimensions.height,
                        width: 20 / 414 * dimensions.width,
                        child: Image.asset(homeScreenImages["fuel_tree_icon"]!))
                  ],
                ),
                SizedBox(
                  height: 4 / 896 * dimensions.height,
                ),
                Text(
                  homeScreenText["text6"]!,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(
                  height: 40 / 896 * dimensions.height,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          SizedBox(
                              height: 20 / 896 * dimensions.height,
                              child: Image.asset(
                                homeScreenImages["rupee_icon"]!,
                                color: Theme.of(context)
                                    .primaryTextTheme
                                    .headlineLarge
                                    ?.color,
                              )),
                          Expanded(
                            child: AutoSizeText(formattedSavedAmount,
                                maxLines: 1,
                                style: Theme.of(context)
                                    .primaryTextTheme
                                    .headlineLarge),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Row(
                      children: [
                        SizedBox(
                            width: 12 / 414 * dimensions.width,
                            height: 12 / 896 * dimensions.height,
                            child: Image.asset(
                              homeScreenImages["rupee_icon"]!,
                              color:
                                  Theme.of(context).textTheme.labelSmall?.color,
                            )),
                        Text(
                          formattedSpent,
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    )
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      homeScreenText["text8"]!,
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                    Text(
                      homeScreenText["text9"]!,
                      style: Theme.of(context).textTheme.labelSmall,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          width: 8 / 414 * dimensions.width,
        ),
        Expanded(
          child: SizedBox(
            width: 149 / 414 * dimensions.width,
            child: Column(children: [
              SizedBox(
                height: 16 / 896 * dimensions.height,
              ),
              Align(
                alignment: Alignment.topRight,
                child: Text(
                  homeScreenText["text10"]!,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
              ),
              SizedBox(
                height: 5 / 896 * dimensions.height,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      height: 130 / 896 * dimensions.height,
                      width: 40 / 414 * dimensions.width,
                      child: getFuelSavingTiles(
                          dimensions, spent, equivalent, true)),
                  Flexible(
                    child: SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                  ),
                  SizedBox(
                      height: 130 / 896 * dimensions.height,
                      width: 40 / 414 * dimensions.width,
                      child: getFuelSavingTiles(
                          dimensions, spent, equivalent, false)),
                  Flexible(
                    child: SizedBox(
                      width: 12 / 414 * dimensions.width,
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                              width: 12 / 414 * dimensions.width,
                              height: 12 / 896 * dimensions.height,
                              child: Image.asset(
                                homeScreenImages["rupee_icon"]!,
                                color: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.color,
                              )),
                          Text(
                            formattedEquivalent,
                            style: Theme.of(context).textTheme.labelSmall,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 80 / 896 * dimensions.height,
                      ),
                      SizedBox(
                        height: 24 / 896 * dimensions.height,
                        width: 24 / 414 * dimensions.width,
                        child: Image.asset(
                          homeScreenImages["fuel_icon"]!,
                          color: Theme.of(context).textTheme.labelSmall?.color,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ]),
          ),
        ),
      ],
    ),
  );
}

getFuelSavingTiles(Dimensions dimensions, int totalSpent, int fuelEquivalent,
    bool isItSpentTitle) {
  int relativeTotalSaved = 0;
  if (isItSpentTitle == true && fuelEquivalent != 0) {
    relativeTotalSaved = 14 - ((totalSpent / fuelEquivalent) * 14).round();
  }

  return ListView.builder(
    padding: EdgeInsets.zero,
    physics: const NeverScrollableScrollPhysics(),
    itemCount: 14,
    itemBuilder: (context, index) {
      return Column(
        children: [
          Container(
            height: 6 / 896 * dimensions.height,
            width: 40 / 414 * dimensions.width,
            decoration: BoxDecoration(
              color: isItSpentTitle == true
                  ? (index < relativeTotalSaved
                      ? colorGrey300
                      : colorBlueFuelTile)
                  : colorGrey300,
              borderRadius: BorderRadius.all(
                  Radius.circular(40 / 414 * dimensions.width)),
            ),
          ),
          SizedBox(height: 3 / 896 * dimensions.height)
        ],
      );
    },
  );
}

scanForDevice() async {
  isGPSModeEnabled = false;
  BluetoothService ble = BluetoothService();
  // ble.resetService();
  await ble.checkPermission();

  bool isBluetoothReady = ble.checkIsBluetoothReady();
  if (!isBluetoothReady) {
    await Future.delayed(const Duration(seconds: 1));
    isBluetoothReady = ble.checkIsBluetoothReady();
    if (!isBluetoothReady) {
      // CustomToast.error(scanDevicesScreen['error1'] ?? "");
      return;
    }
  }
  if (await ble.checkPermission()) {
    // ignore: use_build_context_synchronously
    StreamSubscription<DiscoveredDevice> subscription =
        ble.scanDevices().listen((event) {
      debugPrint(
          "-----device ${event.name.toString()}--------- ${event.id}--------${event.manufacturerData}");
      if (event.name.toString().startsWith("pico0s")) {
        debugPrint("------HURRAY found it");
        isGPSModeEnabled = true;
      }
    });
    await Future.delayed(const Duration(seconds: 3));
    subscription.cancel();
  } else {
    // CustomToast.error(scanDevicesScreen['error2'] ?? "");
    return;
  }
}

Widget getMyActivitiesContainer(BuildContext context, Dimensions dimensions) {

  return FutureBuilder<VehicleTestDetails>(
      future: VehicleTestRepository().getVehicleTestDetails(),
      builder: (context, snapshot) {
        VehicleTestDetails testDetails =
            snapshot.hasData ? snapshot.data! : VehicleTestDetails.empty();

        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: 8 / 414 * dimensions.width,
            vertical: 16 / 896 * dimensions.height,
          ),
          margin: EdgeInsets.only(
            bottom: 16,
            left: 6 / 414 * dimensions.width,
            right: 6 / 414 * dimensions.width,
          ),
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(width: 1, color: colorGrey200),
              boxShadow: [
                BoxShadow(
                    color: colorBlack.withOpacity(0.25),
                    offset: const Offset(1, 3),
                    blurRadius: 3,
                    spreadRadius: 1),
                BoxShadow(
                    color: colorWhite.withOpacity(0.25),
                    offset: const Offset(-1, -3),
                    blurRadius: 3,
                    spreadRadius: 1)
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: 8 / 414 * dimensions.width,
                  bottom: 16 / 896 * dimensions.height,
                ),
                child: Text(
                  homeScreenText["text23"]!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 4 / 414 * dimensions.width,
                        vertical: 20 / 896 * dimensions.height,
                      ),
                      margin: EdgeInsets.only(
                        bottom: 16,
                        left: 6 / 414 * dimensions.width,
                        right: 6 / 414 * dimensions.width,
                      ),
                      decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(width: 1, color: colorGrey200),
                          boxShadow: [
                            BoxShadow(
                                color: colorBlack.withOpacity(0.25),
                                offset: const Offset(1, 3),
                                blurRadius: 3,
                                spreadRadius: 1),
                            BoxShadow(
                                color: colorWhite.withOpacity(0.25),
                                offset: const Offset(-1, -3),
                                blurRadius: 3,
                                spreadRadius: 1)
                          ]),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            homeScreenText["text24"]!,
                            style: Theme.of(context).textTheme.labelMedium,
                          ),
                          Text(
                            homeScreenText["text25"]!,
                            style: Theme.of(context).textTheme.labelMedium,
                          ),
                          SizedBox(height: 16 / 896 * dimensions.height),
                          Text(
                            "${getTimeInHourAndMinFromMins(testDetails.rideTime)[0]} ${insightsText["text11"] ?? "hr"} ${getTimeInHourAndMinFromMins(testDetails.rideTime)[1]} ${insightsText["text12"] ?? "min"}",
                            style: Theme.of(context).primaryTextTheme.bodyLarge,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(width: 4 / 414 * dimensions.width),
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 4 / 414 * dimensions.width,
                        vertical: 20 / 896 * dimensions.height,
                      ),
                      margin: EdgeInsets.only(
                        bottom: 16,
                        left: 6 / 414 * dimensions.width,
                        right: 6 / 414 * dimensions.width,
                      ),
                      decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(width: 1, color: colorGrey200),
                          boxShadow: [
                            BoxShadow(
                                color: colorBlack.withOpacity(0.25),
                                offset: const Offset(1, 3),
                                blurRadius: 3,
                                spreadRadius: 1),
                            BoxShadow(
                                color: colorWhite.withOpacity(0.25),
                                offset: const Offset(-1, -3),
                                blurRadius: 3,
                                spreadRadius: 1)
                          ]),
                      child: Column(
                        children: [
                          Text(
                            homeScreenText["text24"]!,
                            style: Theme.of(context).textTheme.labelMedium,
                          ),
                          AutoSizeText(
                            homeScreenText["text26"]!,
                            style: Theme.of(context).textTheme.labelMedium,
                            maxLines: 1,
                          ),
                          SizedBox(height: 16 / 896 * dimensions.height),
                          Text(
                            '${testDetails.rideDistance.toStringAsFixed(2)} ${insightsText["text10"] ?? "km"}',
                            style: Theme.of(context).primaryTextTheme.bodyLarge,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: 8 / 414 * dimensions.width,
                  top: 16 / 896 * dimensions.height,
                ),
                child: Row(
                  children: [
                    Text(
                      homeScreenText["text27"]!,
                      style: Theme.of(context).textTheme.labelMedium,
                    ),
                    SizedBox(width: 8 / 414 * dimensions.width),
                    SizedBox(
                        width: 16 / 414 * dimensions.width,
                        child: Image.asset(homeScreenImages['distance_icon']!)),
                    SizedBox(width: 8 / 414 * dimensions.width),
                    Text(
                      "${testDetails.testRecords} ${homeScreenText["text28"]!}",
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      });
}

List<String> getTimeInHourAndMinFromMins(double? min) {
  String hours = '0';
  String mins = '0';
  if (min != null) {
    double hour = min / 60;
    int truncHour = hour.truncate();
    hours = truncHour.toString();
    mins = ((hour - truncHour) * 60).truncate().toString();
  }
  return [hours, mins];
}
