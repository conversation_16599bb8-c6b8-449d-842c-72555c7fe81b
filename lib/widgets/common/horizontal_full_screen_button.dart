import 'package:flutter/cupertino.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';

class HorizontalFullScreenButton extends StatelessWidget {
  final String buttonText;
  final Color color;
  const HorizontalFullScreenButton(
      {super.key, required this.buttonText, required this.color});
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Container(
      height: 0.067 * dimensions.height,
      width: 0.88 * dimensions.width,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        color: color,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            buttonText,
            style: urbanistTextStyle(
                0.02 * dimensions.height, colorWhite, FontWeight.w600),
          )
        ],
      ),
    );
  }
}
