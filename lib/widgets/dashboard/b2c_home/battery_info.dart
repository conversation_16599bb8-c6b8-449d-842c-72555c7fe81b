import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/connected_vehicle_status.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/utils/calculate_data.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/battery_indicator.dart';
import 'package:nds_app/widgets/dashboard/b2c_home/toggle_mode.dart';

class BatteryInfo extends StatefulWidget {
  final VehicleInfo vehicleInfo;

  const BatteryInfo({super.key, required this.vehicleInfo});

  @override
  State<BatteryInfo> createState() => _BatteryInfoState();
}

class _BatteryInfoState extends State<BatteryInfo> {
  late VehicleInfo vehicleInfo;
  List<RidingModes> ridingModes = [];

  @override
  void initState() {
    vehicleInfo = widget.vehicleInfo;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    VehicleDataStream stream = VehicleDataStream();
    int charge = widget.vehicleInfo.charge ?? 0;
    ConnectedVehicleStatus status = getBattryStatus(
        widget.vehicleInfo.batteryCharging ?? false,
        widget.vehicleInfo.batteryConnected ?? true);
    bool isBatteryRemoved = status == ConnectedVehicleStatus.batteryRemoved;

    RidingModes ridingMode = RidingModes.values
        .byName((widget.vehicleInfo.currentDriveMode ?? "eco").toLowerCase());

    VehicleModeInfo vehicleModeInfoCurrentMode =
        vehicleInfo.vehicleModeInfoList!.firstWhere(
            (e) => e.mode?.toLowerCase() == ridingMode.name.toLowerCase());

    double maxRangeCurrentMode = vehicleModeInfoCurrentMode.maxRange ?? 0;
    double rangeCorrectionCurrentMode =
        vehicleModeInfoCurrentMode.rangeCorrection ?? 0;

    Dimensions dimensions = Dimensions(context);
    final Color batteryFontColor = charge < 55 ? colorGrey800 : colorGrey25;

    return StreamBuilder(
        stream: stream.vehicleInfo,
        builder: (context, snapshot) {
          if (snapshot.data != null) {
            vehicleInfo = snapshot.data ?? widget.vehicleInfo;
            charge = snapshot.data?.charge ?? 0;

            status = getBattryStatus(snapshot.data?.batteryCharging ?? false,
                snapshot.data?.batteryConnected ?? true);
            isBatteryRemoved = status == ConnectedVehicleStatus.batteryRemoved;
            ridingMode = RidingModes.values.byName(
                (snapshot.data?.currentDriveMode ?? "eco").toLowerCase());
            vehicleModeInfoCurrentMode = vehicleInfo.vehicleModeInfoList!
                .firstWhere((e) =>
                    e.mode?.toLowerCase() == ridingMode.name.toLowerCase());
            maxRangeCurrentMode = vehicleModeInfoCurrentMode.maxRange ?? 0;
            rangeCorrectionCurrentMode =
                vehicleModeInfoCurrentMode.rangeCorrection ?? 0;
          }
          ridingModes.clear();

          vehicleInfo.vehicleModeInfoList?.forEach((element) {
            if (element.mode!.toLowerCase() != 'reverse') {
              ridingModes.add(
                  RidingModes.values.byName((element.mode!).toLowerCase()));
            }
          });
          ridingModes.sort((a, b) => b.index.compareTo(a.index));
          int range = ((charge / 100) * maxRangeCurrentMode -
                  rangeCorrectionCurrentMode)
              .floor();
          return Stack(
            children: [
              BatteryIndicator(
                  percentage: charge.round(),
                  isBatteryRemoved: isBatteryRemoved),
              Positioned(
                top: (isBatteryRemoved ? 30 : 5) / 896 * dimensions.height,
                left: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isBatteryRemoved)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${range < 0 ? 0 : range} ",
                            textScaler: TextScaler.noScaling,
                            style: poppinsTextStyle(30 / 414 * dimensions.width,
                                batteryFontColor, FontWeight.w500),
                          ),
                          Text("km",
                              textScaler: TextScaler.noScaling,
                              style: poppinsTextStyle(
                                  30 / 414 * dimensions.width,
                                  batteryFontColor,
                                  FontWeight.w400)),
                          Tooltip(
                            decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .dividerColor
                                    .withOpacity(0.8),
                                borderRadius: BorderRadius.circular(
                                    4 / 414 * dimensions.width)),
                            preferBelow: false,
                            margin: EdgeInsets.only(
                                left: 150 / 414 * dimensions.width),
                            triggerMode: TooltipTriggerMode.tap,
                            verticalOffset: 12 / 896 * dimensions.height,
                            message: clusterScreenText["range"]!,
                            textStyle: Theme.of(context)
                                .textTheme
                                .labelMedium
                                ?.copyWith(color: colorWhite),
                            child: Icon(
                              Icons.info_outline,
                              color: batteryFontColor,
                            ),
                          )
                        ],
                      ),
                    ToggleMode(
                      currentMode: ridingMode,
                      rideModes: ridingModes,
                    ),
                  ],
                ),
              ),
              if (isBatteryRemoved)
                Positioned(
                    top: 0,
                    right: 32 / 414 * dimensions.width,
                    bottom: 0,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          homeScreenText["battery_removed"]!,
                          textScaler: TextScaler.noScaling,
                          style: poppinsTextStyle(24 / 414 * dimensions.width,
                              colorGrey25, FontWeight.w500),
                        ),
                      ],
                    )),
              if (!isBatteryRemoved)
                Positioned(
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 20 / 414 * dimensions.width,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bolt,
                          color: batteryFontColor,
                          size: 24,
                        ),
                        Text("${charge.round()}%",
                            textScaler: TextScaler.noScaling,
                            style: poppinsTextStyle(30 / 414 * dimensions.width,
                                batteryFontColor, FontWeight.w500))
                      ],
                    )),
            ],
          );
        });
  }
}
