import 'package:flutter/material.dart';

import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/screens/dashboard/select_fleet.dart';
import 'package:nds_app/utils/extension.dart';

import '../../../common/colors.dart';
import '../../common/common_widget.dart';
import '../../../common/constant.dart';
import '../../../common/dummy_data.dart';
import '../../../common/image_urls.dart';
import '../../../common/strings.dart';
import '../../../constant/vehicle_status.dart';

class ScanAndFuelSaving extends StatefulWidget {
  final Function() action;
  final UserInfo? userInfo;
  const ScanAndFuelSaving(
      {super.key, required this.action, required this.userInfo});

  @override
  State<ScanAndFuelSaving> createState() => _ScanAndFuelSavingState();
}

class _ScanAndFuelSavingState extends State<ScanAndFuelSaving> {
  @override
  Widget build(BuildContext context) {
    int spent = widget.userInfo?.spent ?? 0;
    int equivalent = widget.userInfo?.equivalent ?? 0;
    int saved = equivalent - spent;

    Color color = getColor();

    Dimensions dimensions = Dimensions(context);
    return Column(
      children: [
        Visibility(
          visible: !isB2CUser,
          child: getTapToScanContainerButton(() async {
            if (currentVehicleStatus == VehicleStatus.disconnected) {
              // ignore: use_build_context_synchronously
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const SelectFleet()),
              );
            } else {
              await DialogAction.disconnect.action(context: context);
            }
          }, colorGrey800, dimensions),
        ),
        Visibility(
          visible: !isB2CUser,
          child: SizedBox(
            height: 16 / 896 * dimensions.height,
          ),
        ),
        isProdRedUSer
            ? getMyActivitiesContainer(context, dimensions)
            : getFuelSavingDetailsContainer(
                () {}, color, dimensions, saved, equivalent, spent, context),
        SizedBox(
          height: 16 / 896 * dimensions.height,
        ),
        Visibility(
          visible: !isProdRedUSer,
          child: getFuelSavingAndTotalDistanceRow(
              dimensions,
              false,
              saved,
              widget.userInfo?.distanceCovered ?? 0,
              userData["distance_unit"]!,
              context),
        ),
      ],
    );
  }

  Widget getTapToScanContainerButton(
      Function action, Color color, Dimensions dimensions) {
    return InkWell(
      onTap: () async {
        action.call();
      },
      child: Stack(
        children: [
          Container(
            height: 84 / 896 * dimensions.height,
            width: 374 / 414 * dimensions.width,
            margin: EdgeInsets.only(
                bottom: 16,
                left: 6 / 414 * dimensions.width,
                right: 6 / 414 * dimensions.width),
            decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(width: 1, color: colorGrey200),
                boxShadow: [
                  BoxShadow(
                      color: colorBlack.withOpacity(0.25),
                      offset: const Offset(1, 3),
                      blurRadius: 3,
                      spreadRadius: 1),
                  BoxShadow(
                      color: colorWhite.withOpacity(0.25),
                      offset: const Offset(-1, -3),
                      blurRadius: 3,
                      spreadRadius: 1)
                ]),
            child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 20 / 414 * dimensions.width,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          homeScreenText["text3"]!,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Flexible(
                          child: SizedBox(
                            height: 4 / 896 * dimensions.height,
                          ),
                        ),
                        Text(
                          homeScreenText["text4"]!,
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                    SizedBox(
                        width: 36 / 414 * dimensions.width,
                        height: 36 / 896 * dimensions.height,
                        child: Image.asset(
                          homeScreenImages['scan_icon']!,
                          fit: BoxFit.fill,
                          color: Theme.of(context).primaryColorDark,
                        ))
                  ],
                )),
          ),
        ],
      ),
    );
  }

  Color getColor() {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
    Color color = hexColorInStr.isNotEmpty && !isB2CUser
        ? hexColorInStr.toColor()
        : colorGrey800;

    return color;
  }
}
