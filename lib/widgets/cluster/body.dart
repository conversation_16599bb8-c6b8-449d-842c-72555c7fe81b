import 'package:flutter/material.dart';
import 'package:nds_app/constant/riding_modes.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/widgets/cluster/mode.dart';
import 'package:nds_app/widgets/cluster/range.dart';
import 'package:nds_app/widgets/cluster/speedometer.dart';
import 'dynamic_panel.dart';

class ClusterBody extends StatefulWidget {
  final bool isPortrait;
  final VehicleInfo vehicleInfo;
  const ClusterBody(
      {super.key, required this.isPortrait, required this.vehicleInfo});

  @override
  State<ClusterBody> createState() => _ClusterBodyState();
}

class _ClusterBodyState extends State<ClusterBody> {
  VehicleDataStream stream = VehicleDataStream();
  late VehicleInfo vehicleInfo;
  @override
  void initState() {
    vehicleInfo = widget.vehicleInfo;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.isPortrait
        ? Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }

                    return ClusterMode(
                      isPortrait: widget.isPortrait,
                      ridingMode:
                          getRideMode(vehicleInfo.currentDriveMode ?? "eco"),
                    );
                  }),
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterSpeedometer(
                      diMotion: vehicleInfo.diMotion ?? false,
                      isPortrait: widget.isPortrait,
                      odoInKm: vehicleInfo.totalDistance ?? 0,
                      speed: vehicleInfo.speed ?? 0,
                    );
                  }),
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterRange(
                      isPortrait: widget.isPortrait,
                      rangeInKm: getEstimatedRangeOfCurrentMode(
                          vehicleInfo, vehicleInfo.charge!),
                    );
                  }),
              StreamBuilder(
                stream: stream.vehicleInfo,
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    vehicleInfo = snapshot.data!;
                  }
                  return ClusterDynamicPanel(
                      isPortrait: widget.isPortrait, vehicleInfo: vehicleInfo);
                },
              ),
            ],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              StreamBuilder(
                  stream: stream.vehicleInfo,
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      vehicleInfo = snapshot.data!;
                    }
                    return ClusterSpeedometer(
                      diMotion: vehicleInfo.diMotion ?? false,
                      isPortrait: widget.isPortrait,
                      odoInKm: vehicleInfo.totalDistance ?? 0,
                      speed: vehicleInfo.speed ?? 0,
                    );
                  }),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  StreamBuilder(
                      stream: stream.vehicleInfo,
                      builder: (context, snapshot) {
                        if (snapshot.data != null) {
                          vehicleInfo = snapshot.data!;
                        }
                        return ClusterMode(
                          isPortrait: widget.isPortrait,
                          ridingMode:
                              getRideMode(vehicleInfo.currentDriveMode ?? ""),
                        );
                      }),
                  StreamBuilder(
                      stream: stream.vehicleInfo,
                      builder: (context, snapshot) {
                        return ClusterRange(
                          isPortrait: widget.isPortrait,
                          rangeInKm: getEstimatedRangeOfCurrentMode(
                              vehicleInfo, vehicleInfo.charge!),
                        );
                      }),
                ],
              ),
              StreamBuilder(
                stream: stream.vehicleInfo,
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    vehicleInfo = snapshot.data!;
                  }
                  return ClusterDynamicPanel(
                      isPortrait: widget.isPortrait, vehicleInfo: vehicleInfo);
                },
              ),
            ],
          );
  }

  RidingModes getRideMode(String mode) {
    return RidingModes.values.byName((mode).toLowerCase());
  }

  double getEstimatedRangeOfCurrentMode(VehicleInfo vehicleInfo, int soc) {
    double maxRange = 0;
    double correction = 0;
    RidingModes currentDriveMode =
        getRideMode(vehicleInfo.currentDriveMode ?? "");
    vehicleInfo.vehicleModeInfoList?.forEach((element) {
      if (element.mode!.toLowerCase() != "reverse") {
        RidingModes mode = RidingModes.values.firstWhere((e) =>
            e.name.toLowerCase() == (element.mode.toString()).toLowerCase());
        if (mode.name == currentDriveMode.name) {
          maxRange = element.maxRange ?? 0;
          correction = element.rangeCorrection ?? 0;
        }
      }
    });
    return (soc / 100) * maxRange - correction;
  }
}
