import 'package:nds_app/constant/riding_modes.dart';

import '../common/image_urls.dart';
import '../constant/connected_vehicle_status.dart';

String getTimeInCorrectFormatByMinutes(int mins) {
  int hours = (mins / 60).floor();

  int leftMin = (mins % 60);

  String timeInString = "";

  if (leftMin != 0) {
    timeInString = "$timeInString${leftMin}min";
  }
  if (hours != 0) {
    timeInString = "${hours}hr $timeInString";
  }

  return timeInString;
}

ConnectedVehicleStatus getVehicleStatusConstantFromString(String strStatus) {
  ConnectedVehicleStatus status = ConnectedVehicleStatus.connected;

  status = ConnectedVehicleStatus.values
      .firstWhere((e) => e.toString() == 'ConnectedVehicleStatus.$strStatus');

  return status;
}

ConnectedVehicleStatus getBattryStatus(
    bool batteryCharging, bool batteryConnected) {
  ConnectedVehicleStatus status = ConnectedVehicleStatus.connected;
  if (batteryCharging == true) {
    status = ConnectedVehicleStatus.charging;
  } else if (batteryConnected == false) {
    status = ConnectedVehicleStatus.batteryRemoved;
  }
  return status;
}

String getClusterImageUrlByMode(bool isPortrait, RidingModes mode) {
  late String imageUrl;

  if (isPortrait == true) {
    imageUrl = clusterScreenImages["eco_portrait_mode"]!;
    if (mode.name == "city") {
      imageUrl = clusterScreenImages["city_portrait_mode"]!;
    } else if (mode.name == "power") {
      imageUrl = clusterScreenImages["power_portrait_mode"]!;
    }else if (mode.name == "reverse") {
      imageUrl = clusterScreenImages["reverse_portrait_mode"]!;
    }
  } else {
    imageUrl = clusterScreenImages["eco_landscape_mode"]!;
    if (mode.name == "city") {
      imageUrl = clusterScreenImages["city_landscape_mode"]!;
    } else if (mode.name == "power") {
      imageUrl = clusterScreenImages["power_landscape_mode"]!;
    }else if (mode.name == "reverse") {
      imageUrl = clusterScreenImages["reverse_landscape_mode"]!;
    }
  }
  return imageUrl;
}
