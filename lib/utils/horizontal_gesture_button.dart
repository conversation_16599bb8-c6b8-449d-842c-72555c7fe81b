import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/enums/color_type.dart';

class HorizontalGestureButton extends StatefulWidget {
  final String label;
  final Color vehicleColor;
  final Color textColor;
  final ColorType vehicleColorType;
  final double width;
  final double height;
  final Function action;
  const HorizontalGestureButton({
    super.key,
    required this.label,
    required this.vehicleColor,
    required this.textColor,
    required this.width,
    required this.height,
    required this.action,
    required this.vehicleColorType,
  });

  @override
  State<HorizontalGestureButton> createState() =>
      _HorizontalGestureButtonState();
}

class _HorizontalGestureButtonState extends State<HorizontalGestureButton> {
  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return GestureDetector(
      onTap: () async {
        widget.action.call();
      },
      child: Container(
        height: widget.height,
        width: widget.width,
        margin: EdgeInsets.symmetric(horizontal: 8 / 414 * dimensions.width),
        decoration: BoxDecoration(
            color: currentVehicleStatus == VehicleStatus.connected ||
                    colorGrey200 == widget.vehicleColor
                ? widget.vehicleColorType == ColorType.light
                    ? Theme.of(context).highlightColor
                    : widget.vehicleColor
                : loginThemeColor,
            borderRadius: BorderRadius.all(
                Radius.circular(1000 / 414 * dimensions.width))),
        child: Center(
          child: Text(
            widget.label,
            style: poppinsTextStyle(
                16 / 414 * dimensions.width, widget.textColor, FontWeight.w500),
          ),
        ),
      ),
    );
  }
}
