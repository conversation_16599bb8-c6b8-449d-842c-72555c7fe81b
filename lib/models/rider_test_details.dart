class VehicleTestDetails {
  final String? regNo;
  final List<VehicleImage>? images;
  final int? startTime;
  final int? endTime;
  final String? startAddress;
  final String? endAddress;
  final double rideTime;
  final double rideDistance;
  final int testRecords;

  VehicleTestDetails({
    this.regNo,
    this.images,
    this.startTime,
    this.endTime,
    this.startAddress,
    this.endAddress,
    required this.rideTime,
    required this.rideDistance,
    required this.testRecords,
  });

  factory VehicleTestDetails.fromJson(Map<String, dynamic> json) {
    List<VehicleImage> imagesList = [];
    if (json['images'] != null) {
      imagesList = List<VehicleImage>.from(
        (json['images'] as List).map(
          (image) => VehicleImage.fromJson(image),
        ),
      );
    }

    return VehicleTestDetails(
      regNo: json['regNo'],
      images: imagesList,
      startTime: json['startTime'],
      endTime: json['endTime'],
      startAddress: json['startAddress'],
      endAddress: json['endAddress'],
      rideTime: (json['rideTime'] ?? 0).toDouble(),
      rideDistance: (json['rideDistance'] ?? 0).toDouble(),
      testRecords: (json['testRecords'] ?? 0).toInt(),
    );
  }

  factory VehicleTestDetails.empty() {
    return VehicleTestDetails(
      regNo: null,
      images: [],
      startTime: null,
      endTime: null,
      startAddress: null,
      endAddress: null,
      rideTime: 0.0,
      rideDistance: 0.0,
      testRecords: 0,
    );
  }
}

class VehicleImage {
  final String? tag;
  final String? url;

  VehicleImage({
    this.tag,
    this.url,
  });

  factory VehicleImage.fromJson(Map<String, dynamic> json) {
    return VehicleImage(
      tag: json['tag'],
      url: json['url'],
    );
  }
}
