import 'package:nds_app/models/image_url.dart';

class NearbyPOI {
  List<NearByVehicle>? nearByVehicles;
  List? nearByChargingStations;

  NearbyPOI({this.nearByVehicles, this.nearByChargingStations});

  NearbyPOI.fromJson(Map<String, dynamic> json) {
    if (json['nearByVehicles'] != null) {
      nearByVehicles = <NearByVehicle>[];
      json['nearByVehicles'].forEach((v) {
        nearByVehicles!.add(NearByVehicle.fromJson(v));
      });
    }
    if (json['nearByChargingStations'] != null) {
      nearByChargingStations = [];
      // json['nearByChargingStations'].forEach((v) {
      //   nearByChargingStations!.add(new Null.fromJson(v));
      // });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (nearByVehicles != null) {
      data['nearByVehicles'] = nearByVehicles!.map((v) => v.toJson()).toList();
    }
    if (nearByChargingStations != null) {
      data['nearByChargingStations'] =
          nearByChargingStations!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NearByVehicle {
  String? imei;
  double? latitude;
  double? longitude;
  String? regNo;
  int? charge;
  int? distance;
  List<ImageUrl>? images;
  String? distanceUnit;
  int? locationRecordedTime;

  NearByVehicle({
    this.imei,
    this.latitude,
    this.longitude,
    this.regNo,
    this.charge,
    this.distance,
    this.images,
    this.distanceUnit,
    this.locationRecordedTime,
  });

  NearByVehicle.fromJson(Map<String, dynamic> json) {
    imei = json['imei'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    regNo = json['regNo'];
    charge = json['charge'];
    distance = json['distance'];
    distanceUnit = json['distanceUnit'];
    locationRecordedTime = json['locationRecordedTime'];

    images = <ImageUrl>[];
    if (json['images'] != null) {
      json['images'].forEach((v) {
        images!.add(ImageUrl.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['imei'] = imei;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['regNo'] = regNo;
    data['charge'] = charge;
    data['distance'] = distance;
    data['distanceUnit'] = distanceUnit;
    data['locationRecordedTime'] = locationRecordedTime;

    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
