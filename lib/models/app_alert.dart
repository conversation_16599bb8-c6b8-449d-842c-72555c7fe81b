import 'package:nds_app/models/enums/alert_type.dart';

class AppAlert {
  TempAlert? tempAlert;

  AppAlert({this.tempAlert});

  AppAlert.fromJson(Map<String, dynamic> json) {
    tempAlert = TempAlert.fromJson(json['tempAlert']);
  }
}

class TempAlert {
  TempAlertType? tempAlertType;
  double currentTemp = 0;
  double? baseTemp;
  TempAlert({
    this.tempAlertType,
    required this.currentTemp,
    this.baseTemp,
  });

  TempAlert.fromJson(Map<String, dynamic> json) {
    tempAlertType = TempAlertType.values.firstWhere(
        (e) => e.name == json['alertType'].toString().toLowerCase());
    currentTemp = json['currentTemp'] ?? 0.0;
    baseTemp = json['baseTemp'];
  }
}
